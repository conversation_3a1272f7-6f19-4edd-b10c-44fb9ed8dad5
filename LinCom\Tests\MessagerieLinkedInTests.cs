using System;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using System.IO;
using System.Web;

namespace LinCom.Tests
{
    /// <summary>
    /// Tests pour la modernisation de la messagerie style LinkedIn
    /// </summary>
    [TestClass]
    public class MessagerieLinkedInTests
    {
        private string projectPath;

        [TestInitialize]
        public void Setup()
        {
            // Obtenir le chemin du projet
            projectPath = Path.GetDirectoryName(Path.GetDirectoryName(System.Reflection.Assembly.GetExecutingAssembly().Location));
        }

        [TestMethod]
        public void TestCSSFileExists()
        {
            // Arrange
            string cssPath = Path.Combine(projectPath, "css", "messagerie-linkedin-style.css");

            // Act & Assert
            Assert.IsTrue(File.Exists(cssPath), "Le fichier CSS messagerie-linkedin-style.css doit exister");
        }

        [TestMethod]
        public void TestJavaScriptFileExists()
        {
            // Arrange
            string jsPath = Path.Combine(projectPath, "js", "messagerie-linkedin.js");

            // Act & Assert
            Assert.IsTrue(File.Exists(jsPath), "Le fichier JS messagerie-linkedin.js doit exister");
        }

        [TestMethod]
        public void TestDocumentationFileExists()
        {
            // Arrange
            string docPath = Path.Combine(projectPath, "Documentation", "ModernisationMessagerie_LinkedIn.md");

            // Act & Assert
            Assert.IsTrue(File.Exists(docPath), "Le fichier de documentation doit exister");
        }

        [TestMethod]
        public void TestCSSContainsLinkedInVariables()
        {
            // Arrange
            string cssPath = Path.Combine(projectPath, "css", "messagerie-linkedin-style.css");
            
            // Act
            string cssContent = File.ReadAllText(cssPath);

            // Assert
            Assert.IsTrue(cssContent.Contains("--linkedin-blue"), "Le CSS doit contenir la variable --linkedin-blue");
            Assert.IsTrue(cssContent.Contains("--linkedin-gray"), "Le CSS doit contenir la variable --linkedin-gray");
            Assert.IsTrue(cssContent.Contains("--linkedin-white"), "Le CSS doit contenir la variable --linkedin-white");
        }

        [TestMethod]
        public void TestJavaScriptContainsMainClass()
        {
            // Arrange
            string jsPath = Path.Combine(projectPath, "js", "messagerie-linkedin.js");
            
            // Act
            string jsContent = File.ReadAllText(jsPath);

            // Assert
            Assert.IsTrue(jsContent.Contains("class MessagerieLinkedIn"), "Le JS doit contenir la classe MessagerieLinkedIn");
            Assert.IsTrue(jsContent.Contains("setupEventListeners"), "Le JS doit contenir la méthode setupEventListeners");
            Assert.IsTrue(jsContent.Contains("toggleEmojiPicker"), "Le JS doit contenir la méthode toggleEmojiPicker");
        }

        [TestMethod]
        public void TestMessageriePage_ContainsNewReferences()
        {
            // Arrange
            string aspxPath = Path.Combine(projectPath, "messagerie.aspx");
            
            // Act
            string aspxContent = File.ReadAllText(aspxPath);

            // Assert
            Assert.IsTrue(aspxContent.Contains("messagerie-linkedin-style.css"), 
                "La page doit référencer le nouveau fichier CSS");
            Assert.IsTrue(aspxContent.Contains("messagerie-linkedin.js"), 
                "La page doit référencer le nouveau fichier JS");
        }

        [TestMethod]
        public void TestMessageriePage_ContainsLinkedInStructure()
        {
            // Arrange
            string aspxPath = Path.Combine(projectPath, "messagerie.aspx");
            
            // Act
            string aspxContent = File.ReadAllText(aspxPath);

            // Assert
            Assert.IsTrue(aspxContent.Contains("contact-info"), 
                "La page doit contenir la nouvelle structure contact-info");
            Assert.IsTrue(aspxContent.Contains("message-bubble"), 
                "La page doit contenir la nouvelle structure message-bubble");
            Assert.IsTrue(aspxContent.Contains("chat-header-info"), 
                "La page doit contenir la nouvelle structure chat-header-info");
        }

        [TestMethod]
        public void TestCSSResponsiveDesign()
        {
            // Arrange
            string cssPath = Path.Combine(projectPath, "css", "messagerie-linkedin-style.css");
            
            // Act
            string cssContent = File.ReadAllText(cssPath);

            // Assert
            Assert.IsTrue(cssContent.Contains("@media (max-width: 768px)"), 
                "Le CSS doit contenir des media queries pour tablette");
            Assert.IsTrue(cssContent.Contains("@media (max-width: 480px)"), 
                "Le CSS doit contenir des media queries pour mobile");
        }

        [TestMethod]
        public void TestCSSAnimations()
        {
            // Arrange
            string cssPath = Path.Combine(projectPath, "css", "messagerie-linkedin-style.css");
            
            // Act
            string cssContent = File.ReadAllText(cssPath);

            // Assert
            Assert.IsTrue(cssContent.Contains("@keyframes fadeInUp"), 
                "Le CSS doit contenir l'animation fadeInUp");
            Assert.IsTrue(cssContent.Contains("transition:"), 
                "Le CSS doit contenir des transitions");
        }

        [TestMethod]
        public void TestProjectFileContainsNewFiles()
        {
            // Arrange
            string csprojPath = Path.Combine(projectPath, "LinCom.csproj");
            
            // Act
            string csprojContent = File.ReadAllText(csprojPath);

            // Assert
            Assert.IsTrue(csprojContent.Contains("css\\messagerie-linkedin-style.css"), 
                "Le fichier projet doit référencer le nouveau CSS");
            Assert.IsTrue(csprojContent.Contains("js\\messagerie-linkedin.js"), 
                "Le fichier projet doit référencer le nouveau JS");
            Assert.IsTrue(csprojContent.Contains("ModernisationMessagerie_LinkedIn.md"), 
                "Le fichier projet doit référencer la nouvelle documentation");
        }

        [TestMethod]
        public void TestCSSAccessibility()
        {
            // Arrange
            string cssPath = Path.Combine(projectPath, "css", "messagerie-linkedin-style.css");
            
            // Act
            string cssContent = File.ReadAllText(cssPath);

            // Assert
            Assert.IsTrue(cssContent.Contains(":focus"), 
                "Le CSS doit contenir des styles pour le focus (accessibilité)");
            Assert.IsTrue(cssContent.Contains("outline:"), 
                "Le CSS doit gérer les outlines pour l'accessibilité");
        }

        [TestMethod]
        public void TestJavaScriptErrorHandling()
        {
            // Arrange
            string jsPath = Path.Combine(projectPath, "js", "messagerie-linkedin.js");
            
            // Act
            string jsContent = File.ReadAllText(jsPath);

            // Assert
            Assert.IsTrue(jsContent.Contains("try") || jsContent.Contains("catch"), 
                "Le JS devrait contenir une gestion d'erreur");
            Assert.IsTrue(jsContent.Contains("if (") && jsContent.Contains("&&"), 
                "Le JS devrait contenir des vérifications de sécurité");
        }

        [TestMethod]
        public void TestFilesSizeReasonable()
        {
            // Arrange
            string cssPath = Path.Combine(projectPath, "css", "messagerie-linkedin-style.css");
            string jsPath = Path.Combine(projectPath, "js", "messagerie-linkedin.js");

            // Act
            var cssInfo = new FileInfo(cssPath);
            var jsInfo = new FileInfo(jsPath);

            // Assert
            Assert.IsTrue(cssInfo.Length > 1000, "Le fichier CSS doit avoir une taille raisonnable (>1KB)");
            Assert.IsTrue(cssInfo.Length < 100000, "Le fichier CSS ne doit pas être trop volumineux (<100KB)");
            Assert.IsTrue(jsInfo.Length > 1000, "Le fichier JS doit avoir une taille raisonnable (>1KB)");
            Assert.IsTrue(jsInfo.Length < 100000, "Le fichier JS ne doit pas être trop volumineux (<100KB)");
        }
    }
}
