<!DOCTYPE html>
<html>
<head>
    <title>Générateur d'Avatar par Défaut</title>
</head>
<body>
    <canvas id="avatarCanvas" width="56" height="56"></canvas>
    
    <script>
        // Créer un avatar par défaut simple
        const canvas = document.getElementById('avatarCanvas');
        const ctx = canvas.getContext('2d');
        
        // Fond gris
        ctx.fillStyle = '#E5E7EB';
        ctx.fillRect(0, 0, 56, 56);
        
        // Cercle pour la tête
        ctx.fillStyle = '#9CA3AF';
        ctx.beginPath();
        ctx.arc(28, 20, 8, 0, 2 * Math.PI);
        ctx.fill();
        
        // Corps
        ctx.beginPath();
        ctx.arc(28, 44, 16, 0, Math.PI, true);
        ctx.fill();
        
        // Convertir en image et télécharger
        canvas.toBlob(function(blob) {
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'default-avatar.png';
            a.click();
        });
    </script>
</body>
</html>
