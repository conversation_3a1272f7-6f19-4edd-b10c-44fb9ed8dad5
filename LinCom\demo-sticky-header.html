<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Démo - Header Sticky LinkedIn</title>
    <link href="css/messagerie-linkedin-style.css" rel="stylesheet" type="text/css" />
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #f3f2ef;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        .demo-container {
            max-width: 400px;
            margin: 0 auto;
            height: 600px;
        }
        .demo-header {
            text-align: center;
            margin-bottom: 20px;
            color: #333;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <div class="demo-header">
            <h2>🔒 Header Sticky - Démo</h2>
            <p>Faites défiler la liste des contacts pour voir l'effet sticky</p>
        </div>

        <!-- Panel de contacts avec header sticky -->
        <div class="contacts-panel" style="height: 100%; border: 1px solid #e0e0e0; border-radius: 8px;">
            <!-- En-tête Messages LinkedIn style - STICKY -->
            <div class="linkedin-messages-header">
                <div class="messages-title">
                    <div class="title-main">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor" class="messages-icon">
                            <path d="M20 2H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h4l4 4 4-4h4c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-2 12H6v-2h12v2zm0-3H6V9h12v2zm0-3H6V6h12v2z"/>
                        </svg>
                        <span class="title-text">Messages</span>
                    </div>
                    <div class="messages-actions">
                        <button type="button" class="header-action-btn" title="Nouvelle conversation">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"/>
                            </svg>
                        </button>
                        <button type="button" class="header-action-btn" title="Plus d'options">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M12 8c1.1 0 2-.9 2-2s-.9-2-2-2-2 .9-2 2 .9 2 2 2zm0 2c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm0 6c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z"/>
                            </svg>
                        </button>
                    </div>
                </div>
                
                <!-- Onglets Messages/Conversations -->
                <div class="messages-tabs">
                    <button type="button" class="tab-btn active" data-tab="messages">
                        <span>Messages</span>
                        <span class="tab-count">25</span>
                    </button>
                    <button type="button" class="tab-btn" data-tab="conversations">
                        <span>Conversations</span>
                    </button>
                </div>
            </div>

            <!-- Barre de recherche LinkedIn style - STICKY -->
            <div class="linkedin-search-container">
                <div class="search-input-wrapper">
                    <div class="search-icon">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
                        </svg>
                    </div>
                    <input type="text" placeholder="Rechercher dans les messages..." class="linkedin-search-input" />
                    <div class="search-actions">
                        <button type="button" class="search-filter-btn" title="Filtres de recherche">
                            <svg width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M10 18h4v-2h-4v2zM3 6v2h18V6H3zm3 7h12v-2H6v2z"/>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Liste des contacts scrollable -->
            <div class="contacts-list">
                <!-- Contacts de démonstration -->
                <div class="contact-item active">
                    <img src="https://via.placeholder.com/48x48/0a66c2/ffffff?text=JD" alt="Photo de profil" />
                    <div class="contact-info">
                        <div class="contact-name">Jean Dupont</div>
                        <div class="contact-status">
                            <span class="online-indicator"></span>
                            <span>En ligne</span>
                        </div>
                    </div>
                </div>

                <div class="contact-item">
                    <img src="https://via.placeholder.com/48x48/28a745/ffffff?text=MM" alt="Photo de profil" />
                    <div class="contact-info">
                        <div class="contact-name">Marie Martin</div>
                        <div class="contact-status">
                            <span class="online-indicator"></span>
                            <span>En ligne</span>
                        </div>
                    </div>
                    <div class="notification-badge">2</div>
                </div>

                <div class="contact-item">
                    <img src="https://via.placeholder.com/48x48/6c757d/ffffff?text=PD" alt="Photo de profil" />
                    <div class="contact-info">
                        <div class="contact-name">Pierre Durand</div>
                        <div class="contact-status">
                            <span>Hors ligne</span>
                        </div>
                    </div>
                </div>

                <div class="contact-item">
                    <img src="https://via.placeholder.com/48x48/17a2b8/ffffff?text=AL" alt="Photo de profil" />
                    <div class="contact-info">
                        <div class="contact-name">Alice Leroy</div>
                        <div class="contact-status">
                            <span class="online-indicator"></span>
                            <span>En ligne</span>
                        </div>
                    </div>
                </div>

                <div class="contact-item">
                    <img src="https://via.placeholder.com/48x48/ffc107/ffffff?text=BM" alt="Photo de profil" />
                    <div class="contact-info">
                        <div class="contact-name">Bob Moreau</div>
                        <div class="contact-status">
                            <span>Hors ligne</span>
                        </div>
                    </div>
                </div>

                <div class="contact-item">
                    <img src="https://via.placeholder.com/48x48/dc3545/ffffff?text=CR" alt="Photo de profil" />
                    <div class="contact-info">
                        <div class="contact-name">Claire Rousseau</div>
                        <div class="contact-status">
                            <span class="online-indicator"></span>
                            <span>En ligne</span>
                        </div>
                    </div>
                    <div class="notification-badge">5</div>
                </div>

                <div class="contact-item">
                    <img src="https://via.placeholder.com/48x48/6f42c1/ffffff?text=DL" alt="Photo de profil" />
                    <div class="contact-info">
                        <div class="contact-name">David Lambert</div>
                        <div class="contact-status">
                            <span>Hors ligne</span>
                        </div>
                    </div>
                </div>

                <div class="contact-item">
                    <img src="https://via.placeholder.com/48x48/fd7e14/ffffff?text=EB" alt="Photo de profil" />
                    <div class="contact-info">
                        <div class="contact-name">Emma Bernard</div>
                        <div class="contact-status">
                            <span class="online-indicator"></span>
                            <span>En ligne</span>
                        </div>
                    </div>
                </div>

                <div class="contact-item">
                    <img src="https://via.placeholder.com/48x48/20c997/ffffff?text=FG" alt="Photo de profil" />
                    <div class="contact-info">
                        <div class="contact-name">François Girard</div>
                        <div class="contact-status">
                            <span>Hors ligne</span>
                        </div>
                    </div>
                </div>

                <div class="contact-item">
                    <img src="https://via.placeholder.com/48x48/e83e8c/ffffff?text=GH" alt="Photo de profil" />
                    <div class="contact-info">
                        <div class="contact-name">Gabrielle Hubert</div>
                        <div class="contact-status">
                            <span class="online-indicator"></span>
                            <span>En ligne</span>
                        </div>
                    </div>
                    <div class="notification-badge">1</div>
                </div>

                <div class="contact-item">
                    <img src="https://via.placeholder.com/48x48/495057/ffffff?text=HI" alt="Photo de profil" />
                    <div class="contact-info">
                        <div class="contact-name">Henri Icard</div>
                        <div class="contact-status">
                            <span>Hors ligne</span>
                        </div>
                    </div>
                </div>

                <div class="contact-item">
                    <img src="https://via.placeholder.com/48x48/0dcaf0/ffffff?text=IJ" alt="Photo de profil" />
                    <div class="contact-info">
                        <div class="contact-name">Isabelle Joly</div>
                        <div class="contact-status">
                            <span class="online-indicator"></span>
                            <span>En ligne</span>
                        </div>
                    </div>
                </div>

                <div class="contact-item">
                    <img src="https://via.placeholder.com/48x48/198754/ffffff?text=JK" alt="Photo de profil" />
                    <div class="contact-info">
                        <div class="contact-name">Julien Klein</div>
                        <div class="contact-status">
                            <span>Hors ligne</span>
                        </div>
                    </div>
                </div>

                <div class="contact-item">
                    <img src="https://via.placeholder.com/48x48/b02a37/ffffff?text=KL" alt="Photo de profil" />
                    <div class="contact-info">
                        <div class="contact-name">Karine Leblanc</div>
                        <div class="contact-status">
                            <span class="online-indicator"></span>
                            <span>En ligne</span>
                        </div>
                    </div>
                    <div class="notification-badge">3</div>
                </div>

                <div class="contact-item">
                    <img src="https://via.placeholder.com/48x48/0f5132/ffffff?text=LM" alt="Photo de profil" />
                    <div class="contact-info">
                        <div class="contact-name">Louis Mercier</div>
                        <div class="contact-status">
                            <span>Hors ligne</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div style="margin-top: 20px; text-align: center; color: #666; font-size: 14px;">
            <p>💡 <strong>Astuce :</strong> Faites défiler la liste pour voir l'effet sticky avec les ombres dynamiques</p>
        </div>
    </div>

    <script src="js/messagerie-linkedin.js"></script>
    <script>
        // Script de démonstration pour le sticky header
        document.addEventListener('DOMContentLoaded', function() {
            // Initialiser le sticky header
            if (window.messagerieLinkedIn) {
                window.messagerieLinkedIn.setupStickyHeader();
            } else {
                // Version simplifiée si la classe principale n'est pas disponible
                const contactsList = document.querySelector('.contacts-list');
                const messagesHeader = document.querySelector('.linkedin-messages-header');
                const searchContainer = document.querySelector('.linkedin-search-container');

                if (contactsList) {
                    contactsList.addEventListener('scroll', function() {
                        const scrollTop = this.scrollTop;
                        
                        if (scrollTop > 10) {
                            if (messagesHeader) messagesHeader.classList.add('scrolled');
                            if (searchContainer) searchContainer.classList.add('scrolled');
                        } else {
                            if (messagesHeader) messagesHeader.classList.remove('scrolled');
                            if (searchContainer) searchContainer.classList.remove('scrolled');
                        }
                    });
                }
            }

            // Gestion des onglets
            document.querySelectorAll('.tab-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    document.querySelectorAll('.tab-btn').forEach(b => b.classList.remove('active'));
                    this.classList.add('active');
                });
            });

            // Gestion des contacts
            document.querySelectorAll('.contact-item').forEach(contact => {
                contact.addEventListener('click', function() {
                    document.querySelectorAll('.contact-item').forEach(c => c.classList.remove('active'));
                    this.classList.add('active');
                });
            });
        });
    </script>
</body>
</html>
