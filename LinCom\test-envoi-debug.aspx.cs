using LinCom.Class;
using LinCom.Classe;
using LinCom.Imp;
using LinCom.Model;
using System;
using System.IO;
using System.Linq;
using System.Text;
using System.Web.UI;

namespace LinCom
{
    public partial class test_envoi_debug : System.Web.UI.Page
    {
        IMessage objmes = new MessageImp();
        Message_Class mess = new Message_Class();
        IConversation objconver = new ConversationImp();
        Conversation_Class conver = new Conversation_Class();
        ParticipantConversation_Class partconver = new ParticipantConversation_Class();
        MessageStatus_Class messtatu = new MessageStatus_Class();
        IMembre objmem = new MembreImp();
        Membre_Class mem = new Membre_Class();

        protected void Page_Load(object sender, EventArgs e)
        {
            if (!IsPostBack)
            {
                // Simuler une session utilisateur pour les tests
                if (Session["idmembre"] == null)
                {
                    Session["idmembre"] = 1L; // ID utilisateur de test
                }

                AfficherInfosDebug();
            }
        }

        private void AfficherInfosDebug()
        {
            lblSessionId.Text = Session["idmembre"]?.ToString() ?? "Non défini";
            lblHeureServeur.Text = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
            lblNbFichiers.Text = Request.Files.Count.ToString();
        }

        protected void btnTestMessage_Click(object sender, EventArgs e)
        {
            var logs = new StringBuilder();
            logs.AppendLine("=== TEST ENVOI MESSAGE ===");

            try
            {
                long senderId = Convert.ToInt64(Session["idmembre"]);
                long destinataireId = Convert.ToInt64(txtDestinataire.Value);
                string contenu = txtTestMessage.Value;

                logs.AppendLine($"SenderId: {senderId}");
                logs.AppendLine($"DestinataireId: {destinataireId}");
                logs.AppendLine($"Contenu: '{contenu}'");

                if (string.IsNullOrWhiteSpace(contenu))
                {
                    logs.AppendLine("ERREUR: Contenu vide");
                    lblDebugLogs.Text = logs.ToString().Replace("\n", "<br/>");
                    lblDebugLogs.CssClass = "error";
                    return;
                }

                // Test conversion emojis
                string contenuAvecEmojis = EmojiManager.ConvertirEmojis(contenu);
                logs.AppendLine($"Contenu avec emojis: '{contenuAvecEmojis}'");

                // Test création/récupération conversation
                long conversationId = objconver.VerifierConversationId(senderId, destinataireId);
                logs.AppendLine($"ConversationId existante: {conversationId}");

                if (conversationId <= 0)
                {
                    logs.AppendLine("Création d'une nouvelle conversation...");
                    
                    conver.Type = "prive";
                    conver.DateCreation = DateTime.Now;
                    conver.CreePar = senderId;
                    conver.statut = "actif";
                    
                    int resultConv = objconver.Ajouter(conver);
                    logs.AppendLine($"Résultat création conversation: {resultConv}");
                    
                    if (resultConv == 1)
                    {
                        conversationId = objconver.VerifierConversationId(senderId, destinataireId);
                        logs.AppendLine($"Nouvelle ConversationId: {conversationId}");
                    }
                }

                if (conversationId <= 0)
                {
                    logs.AppendLine("ERREUR: Impossible de créer/récupérer la conversation");
                    lblDebugLogs.Text = logs.ToString().Replace("\n", "<br/>");
                    lblDebugLogs.CssClass = "error";
                    return;
                }

                // Test envoi message
                mess = new Message_Class();
                mess.ConversationId = conversationId;
                mess.SenderId = senderId;
                mess.Contenu = contenuAvecEmojis;
                mess.DateEnvoi = DateTime.Now;
                mess.name = "";
                mess.AttachmentUrl = "";

                logs.AppendLine("Envoi du message...");
                long[] participants = { senderId, destinataireId };
                int resultat = objmes.EnvoyerMessageComplet(mess, participants);
                
                logs.AppendLine($"Résultat envoi: {resultat}");

                if (resultat == 1)
                {
                    logs.AppendLine("✅ MESSAGE ENVOYÉ AVEC SUCCÈS!");
                    lblDebugLogs.CssClass = "success";
                    
                    // Nettoyer le formulaire
                    txtTestMessage.Value = "";
                }
                else
                {
                    logs.AppendLine("❌ ÉCHEC DE L'ENVOI");
                    lblDebugLogs.CssClass = "error";
                }
            }
            catch (Exception ex)
            {
                logs.AppendLine($"EXCEPTION: {ex.Message}");
                logs.AppendLine($"Stack trace: {ex.StackTrace}");
                lblDebugLogs.CssClass = "error";
            }

            lblDebugLogs.Text = logs.ToString().Replace("\n", "<br/>");
        }

        protected void btnTestEmojis_Click(object sender, EventArgs e)
        {
            try
            {
                string texteOriginal = txtEmojis.Value;
                string texteConverti = EmojiManager.ConvertirEmojis(texteOriginal);
                
                lblEmojiResult.Text = $"<strong>Original:</strong> {texteOriginal}<br/>" +
                                     $"<strong>Converti:</strong> {texteConverti}";
                lblEmojiResult.CssClass = "success";
            }
            catch (Exception ex)
            {
                lblEmojiResult.Text = $"Erreur: {ex.Message}";
                lblEmojiResult.CssClass = "error";
            }
        }

        protected void btnTestFile_Click(object sender, EventArgs e)
        {
            var logs = new StringBuilder();
            logs.AppendLine("=== TEST PIÈCE JOINTE ===");

            try
            {
                if (fileTest.PostedFile != null && fileTest.PostedFile.ContentLength > 0)
                {
                    var file = fileTest.PostedFile;
                    logs.AppendLine($"Nom fichier: {file.FileName}");
                    logs.AppendLine($"Taille: {file.ContentLength} bytes");
                    logs.AppendLine($"Type MIME: {file.ContentType}");

                    // Validation du fichier
                    var validation = AttachmentManager.ValiderFichier(file);
                    logs.AppendLine($"Validation: {validation.EstValide}");
                    if (!validation.EstValide)
                    {
                        logs.AppendLine($"Erreur validation: {validation.Message}");
                    }
                    else
                    {
                        // Test upload
                        long membreId = Convert.ToInt64(Session["idmembre"]);
                        var fichierInfo = AttachmentManager.UploadFichier(file, membreId);
                        
                        logs.AppendLine($"✅ FICHIER UPLOADÉ:");
                        logs.AppendLine($"URL: {fichierInfo.UrlAcces}");
                        logs.AppendLine($"Nom original: {fichierInfo.NomOriginal}");
                        logs.AppendLine($"Taille formatée: {AttachmentManager.FormaterTaille(fichierInfo.TailleOctets)}");
                        
                        hdnAttachmentPath.Value = fichierInfo.UrlAcces;
                        lblDebugLogs.CssClass = "success";
                    }
                }
                else
                {
                    logs.AppendLine("❌ Aucun fichier sélectionné");
                    lblDebugLogs.CssClass = "warning";
                }
            }
            catch (Exception ex)
            {
                logs.AppendLine($"EXCEPTION: {ex.Message}");
                lblDebugLogs.CssClass = "error";
            }

            lblDebugLogs.Text = logs.ToString().Replace("\n", "<br/>");
        }

        protected void btnTestDB_Click(object sender, EventArgs e)
        {
            var logs = new StringBuilder();
            logs.AppendLine("=== TEST CONNEXION BASE DE DONNÉES ===");

            try
            {
                using (var con = new Connection())
                {
                    logs.AppendLine("✅ Connexion établie");
                    
                    // Test comptage membres
                    int nbMembres = con.Membres.Count();
                    logs.AppendLine($"Nombre de membres: {nbMembres}");
                    
                    // Test comptage conversations
                    int nbConversations = con.Conversations.Count();
                    logs.AppendLine($"Nombre de conversations: {nbConversations}");
                    
                    // Test comptage messages
                    int nbMessages = con.Messages.Count();
                    logs.AppendLine($"Nombre de messages: {nbMessages}");
                    
                    lblDBResult.CssClass = "success";
                }
            }
            catch (Exception ex)
            {
                logs.AppendLine($"❌ ERREUR CONNEXION: {ex.Message}");
                lblDBResult.CssClass = "error";
            }

            lblDBResult.Text = logs.ToString().Replace("\n", "<br/>");
        }

        protected void btnTestConversation_Click(object sender, EventArgs e)
        {
            var logs = new StringBuilder();
            logs.AppendLine("=== TEST CONVERSATION ===");

            try
            {
                long user1 = Convert.ToInt64(txtUser1.Value);
                long user2 = Convert.ToInt64(txtUser2.Value);
                
                logs.AppendLine($"Utilisateur 1: {user1}");
                logs.AppendLine($"Utilisateur 2: {user2}");

                // Vérifier si conversation existe
                long conversationId = objconver.VerifierConversationId(user1, user2);
                logs.AppendLine($"Conversation existante: {conversationId}");

                if (conversationId <= 0)
                {
                    logs.AppendLine("Création d'une nouvelle conversation...");
                    
                    conver = new Conversation_Class();
                    conver.Type = "prive";
                    conver.DateCreation = DateTime.Now;
                    conver.CreePar = user1;
                    conver.statut = "actif";
                    
                    int result = objconver.Ajouter(conver);
                    logs.AppendLine($"Résultat création: {result}");
                    
                    if (result == 1)
                    {
                        conversationId = objconver.VerifierConversationId(user1, user2);
                        logs.AppendLine($"✅ Nouvelle conversation créée: {conversationId}");
                        lblConversationResult.CssClass = "success";
                    }
                    else
                    {
                        logs.AppendLine("❌ Échec création conversation");
                        lblConversationResult.CssClass = "error";
                    }
                }
                else
                {
                    logs.AppendLine("✅ Conversation existante trouvée");
                    lblConversationResult.CssClass = "success";
                }
            }
            catch (Exception ex)
            {
                logs.AppendLine($"EXCEPTION: {ex.Message}");
                lblConversationResult.CssClass = "error";
            }

            lblConversationResult.Text = logs.ToString().Replace("\n", "<br/>");
        }
    }
}
