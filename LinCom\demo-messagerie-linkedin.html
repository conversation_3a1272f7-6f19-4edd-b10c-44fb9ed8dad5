<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Démo - Messagerie LinkedIn Style</title>
    <link href="css/messagerie-linkedin-style.css" rel="stylesheet" type="text/css" />
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #f3f2ef;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        .demo-container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .demo-header {
            text-align: center;
            margin-bottom: 30px;
            color: #333;
        }
        .demo-features {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .feature-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        .feature-item {
            padding: 10px;
            background: #f8f9fa;
            border-radius: 6px;
            border-left: 3px solid #0a66c2;
        }
        .demo-chat {
            height: 600px;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <div class="demo-header">
            <h1>🚀 Messagerie LinkedIn Style - Démonstration</h1>
            <p>Interface de messagerie modernisée avec toutes les fonctionnalités LinkedIn</p>
        </div>

        <div class="demo-features">
            <h2>✨ Nouvelles fonctionnalités</h2>
            <div class="feature-list">
                <div class="feature-item">
                    <strong>🎨 Formatage de texte</strong><br>
                    Gras, italique, souligné avec raccourcis clavier
                </div>
                <div class="feature-item">
                    <strong>😊 Sélecteur d'émojis</strong><br>
                    Interface moderne avec catégories
                </div>
                <div class="feature-item">
                    <strong>@ Mentions</strong><br>
                    Auto-complétion des contacts
                </div>
                <div class="feature-item">
                    <strong>📎 Pièces jointes</strong><br>
                    Glisser-déposer avec prévisualisation
                </div>
                <div class="feature-item">
                    <strong>⌨️ Raccourcis clavier</strong><br>
                    Ctrl+B, Ctrl+I, Ctrl+U, Ctrl+E
                </div>
                <div class="feature-item">
                    <strong>📱 Responsive</strong><br>
                    Optimisé mobile et tablette
                </div>
            </div>
        </div>

        <!-- Interface de messagerie -->
        <div class="chat-wrapper demo-chat">
            <!-- Panel de contacts -->
            <div class="contacts-panel">
                <div class="contacts-header">
                    <span>💬 Messages</span>
                    <span style="font-size: 12px; font-weight: normal;">Démonstration</span>
                </div>
                <div class="contacts-search">
                    <input type="text" placeholder="Rechercher dans les messages..." />
                </div>
                <div class="contacts-list">
                    <div class="contact-item active">
                        <img src="https://via.placeholder.com/48x48/0a66c2/ffffff?text=JD" alt="Photo de profil" />
                        <div class="contact-info">
                            <div class="contact-name">Jean Dupont</div>
                            <div class="contact-status">
                                <span class="online-indicator"></span>
                                <span>En ligne</span>
                            </div>
                        </div>
                    </div>
                    <div class="contact-item">
                        <img src="https://via.placeholder.com/48x48/28a745/ffffff?text=MM" alt="Photo de profil" />
                        <div class="contact-info">
                            <div class="contact-name">Marie Martin</div>
                            <div class="contact-status">
                                <span class="online-indicator"></span>
                                <span>En ligne</span>
                            </div>
                        </div>
                        <div class="notification-badge">2</div>
                    </div>
                    <div class="contact-item">
                        <img src="https://via.placeholder.com/48x48/6c757d/ffffff?text=PD" alt="Photo de profil" />
                        <div class="contact-info">
                            <div class="contact-name">Pierre Durand</div>
                            <div class="contact-status">
                                <span>Hors ligne</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Panel de chat -->
            <div class="chat-panel">
                <div class="chat-header">
                    <div class="chat-header-info">
                        <div class="chat-header-name">Jean Dupont</div>
                        <div class="chat-header-status">
                            <span class="online-indicator"></span>
                            <span>En ligne maintenant</span>
                        </div>
                    </div>
                </div>

                <div class="chat-body">
                    <!-- Messages de démonstration -->
                    <div class="message-container received">
                        <div class="message-header">
                            <img class="avatar" src="https://via.placeholder.com/32x32/0a66c2/ffffff?text=JD" alt="Photo" />
                            <span class="message-sender">Jean Dupont</span>
                            <span class="message-time">14:30</span>
                        </div>
                        <div class="message-bubble">
                            <div class="message-body">
                                <p>Salut ! Comment ça va ? 😊</p>
                            </div>
                        </div>
                    </div>

                    <div class="message-container sent">
                        <div class="message-header">
                            <img class="avatar" src="https://via.placeholder.com/32x32/28a745/ffffff?text=ME" alt="Photo" />
                            <span class="message-sender">Moi</span>
                            <span class="message-time">14:32</span>
                        </div>
                        <div class="message-bubble">
                            <div class="message-body">
                                <p>Très bien merci ! J'ai testé la nouvelle interface de messagerie, elle est **vraiment** géniale ! 🚀</p>
                            </div>
                        </div>
                        <div class="message-footer">
                            <span class="message-status status-read">✓✓</span>
                        </div>
                    </div>

                    <div class="message-container received">
                        <div class="message-header">
                            <img class="avatar" src="https://via.placeholder.com/32x32/0a66c2/ffffff?text=JD" alt="Photo" />
                            <span class="message-sender">Jean Dupont</span>
                            <span class="message-time">14:35</span>
                        </div>
                        <div class="message-bubble">
                            <div class="message-body">
                                <p>Oui, le design LinkedIn est vraiment réussi ! Les fonctionnalités de formatage sont top 👍</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Zone de saisie LinkedIn-style -->
                <div class="chat-footer">
                    <div class="linkedin-message-composer">
                        <!-- Barre d'outils supérieure -->
                        <div class="composer-toolbar">
                            <div class="toolbar-left">
                                <button type="button" id="btnBold" class="format-btn" title="Gras (Ctrl+B)">
                                    <svg width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M15.6 10.79c.97-.67 1.65-1.77 1.65-2.79 0-2.26-1.75-4-4-4H7v14h7.04c2.09 0 3.71-1.7 3.71-3.79 0-1.52-.86-2.82-2.15-3.42zM10 6.5h3c.83 0 1.5.67 1.5 1.5s-.67 1.5-1.5 1.5h-3v-3zm3.5 9H10v-3h3.5c.83 0 1.5.67 1.5 1.5s-.67 1.5-1.5 1.5z"/>
                                    </svg>
                                </button>
                                <button type="button" id="btnItalic" class="format-btn" title="Italique (Ctrl+I)">
                                    <svg width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M10 4v3h2.21l-3.42 8H6v3h8v-3h-2.21l3.42-8H18V4z"/>
                                    </svg>
                                </button>
                                <button type="button" id="btnUnderline" class="format-btn" title="Souligné (Ctrl+U)">
                                    <svg width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M12 17c3.31 0 6-2.69 6-6V3h-2.5v8c0 1.93-1.57 3.5-3.5 3.5S8.5 12.93 8.5 11V3H6v8c0 3.31 2.69 6 6 6zm-7 2v2h14v-2H5z"/>
                                    </svg>
                                </button>
                                <div class="toolbar-separator"></div>
                                <button type="button" id="btnEmoji" class="format-btn" title="Émojis">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                                    </svg>
                                </button>
                                <button type="button" id="btnAttachment" class="format-btn" title="Joindre un fichier">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M16.5 6v11.5c0 2.21-1.79 4-4 4s-4-1.79-4-4V5c0-1.38 1.12-2.5 2.5-2.5s2.5 1.12 2.5 2.5v10.5c0 .55-.45 1-1 1s-1-.45-1-1V6H10v9.5c0 1.38 1.12 2.5 2.5 2.5s2.5-1.12 2.5-2.5V5c0-2.21-1.79-4-4-4S7 2.79 7 5v12.5c0 3.04 2.46 5.5 5.5 5.5s5.5-2.46 5.5-5.5V6h-1.5z"/>
                                    </svg>
                                </button>
                                <input type="file" id="fileAttachment" style="display:none;" />
                            </div>
                            <div class="toolbar-right">
                                <button type="button" id="btnGif" class="format-btn" title="GIF">
                                    <span style="font-size: 11px; font-weight: bold;">GIF</span>
                                </button>
                                <button type="button" id="btnMore" class="format-btn" title="Plus d'options">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M12 8c1.1 0 2-.9 2-2s-.9-2-2-2-2 .9-2 2 .9 2 2 2zm0 2c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm0 6c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z"/>
                                    </svg>
                                </button>
                            </div>
                        </div>

                        <!-- Zone de saisie principale -->
                        <div class="message-input-wrapper">
                            <div class="input-container">
                                <div class="message-input-area">
                                    <textarea rows="1" id="demoTextarea" placeholder="Écrivez un message..." maxlength="1000" class="linkedin-message-input"></textarea>
                                    <div class="input-placeholder" id="inputPlaceholder">Écrivez un message...</div>
                                </div>
                                <div class="send-button-container">
                                    <button type="button" id="demoSendBtn" class="linkedin-send-btn" disabled>
                                        <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                            <path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"/>
                                        </svg>
                                    </button>
                                </div>
                            </div>
                            
                            <!-- Suggestions -->
                            <div class="suggestions-container" id="suggestionsContainer" style="display: none;">
                                <div class="suggestions-list" id="suggestionsList"></div>
                            </div>
                        </div>

                        <!-- Footer -->
                        <div class="composer-footer">
                            <div class="footer-left">
                                <span class="char-counter" id="charCounter">0/1000</span>
                                <span class="typing-indicator" id="typingIndicator" style="display: none;">
                                    <span class="typing-dots">
                                        <span></span><span></span><span></span>
                                    </span>
                                    En train d'écrire...
                                </span>
                            </div>
                            <div class="footer-right">
                                <button type="button" id="btnSchedule" class="schedule-btn" title="Programmer l'envoi">
                                    <svg width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M11.99 2C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2zM12 20c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8z"/>
                                        <path d="M12.5 7H11v6l5.25 3.15.75-1.23-4.5-2.67z"/>
                                    </svg>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div style="margin-top: 20px; text-align: center; color: #666; font-size: 14px;">
            <p>💡 <strong>Conseils :</strong> Essayez les raccourcis Ctrl+B, Ctrl+I, Ctrl+U pour le formatage. Tapez @ pour les mentions.</p>
        </div>
    </div>

    <script src="js/messagerie-linkedin.js"></script>
    <script>
        // Script de démonstration
        document.addEventListener('DOMContentLoaded', function() {
            // Adapter le script pour la démo
            const textarea = document.getElementById('demoTextarea');
            const sendBtn = document.getElementById('demoSendBtn');
            const charCounter = document.getElementById('charCounter');
            const placeholder = document.getElementById('inputPlaceholder');

            if (textarea) {
                textarea.addEventListener('input', function() {
                    const length = this.value.length;
                    charCounter.textContent = `${length}/1000`;
                    sendBtn.disabled = length === 0;
                    
                    if (this.value.length > 0) {
                        placeholder.classList.add('hidden');
                    } else {
                        placeholder.classList.remove('hidden');
                    }
                    
                    // Auto-resize
                    this.style.height = 'auto';
                    this.style.height = Math.min(this.scrollHeight, 120) + 'px';
                });

                textarea.addEventListener('focus', function() {
                    placeholder.classList.add('hidden');
                });

                textarea.addEventListener('blur', function() {
                    if (this.value.length === 0) {
                        placeholder.classList.remove('hidden');
                    }
                });
            }

            // Simulation des fonctionnalités
            document.getElementById('btnBold')?.addEventListener('click', () => alert('Formatage gras activé !'));
            document.getElementById('btnItalic')?.addEventListener('click', () => alert('Formatage italique activé !'));
            document.getElementById('btnEmoji')?.addEventListener('click', () => alert('Sélecteur d\'émojis à venir !'));
            document.getElementById('btnAttachment')?.addEventListener('click', () => alert('Sélection de fichier !'));
            document.getElementById('btnGif')?.addEventListener('click', () => alert('Sélecteur de GIF !'));
            document.getElementById('btnSchedule')?.addEventListener('click', () => alert('Programmation d\'envoi !'));
        });
    </script>
</body>
</html>
