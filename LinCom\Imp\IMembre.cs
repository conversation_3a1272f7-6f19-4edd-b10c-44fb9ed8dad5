using LinCom.Classe;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web.UI.WebControls;

namespace LinCom.Imp
{
    internal interface IMembre
    {
        void AfficherDetails(long membreId, Membre_Class membre);
        int Ajouter(Membre_Class membre);
        int Modifier(Membre_Class membre, string email, long code, int cd);
        int Supprimer(long membreId);
        void ChargerGridView(GridView gdv, string filtre = "", string name = "");
        void AfficherDetails(string name, Membre_Class membre,int cd);
        int Connect(Membre_Class membre, string usernm, string pwsd, int code);
        void chargerMembre(DropDownList lst);
        bool VerifierMotDePasse(string email, string motDePasseEntree);
        int count(int cd, string publie, string code);
        void ChargerListview(ListView gdv, long id, string statut, string name);
        IEnumerable<object> RechercherMembres(string termeRecherche, long membreExclu);
        DateTime? GetLastActivity(long membreId);

    }
}
