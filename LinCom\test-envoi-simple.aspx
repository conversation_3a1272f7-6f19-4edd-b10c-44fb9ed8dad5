<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="test-envoi-simple.aspx.cs" Inherits="LinCom.test_envoi_simple" %>

<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <title>Test Envoi Simple</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-container { max-width: 600px; margin: 0 auto; }
        .form-group { margin: 15px 0; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input, textarea { width: 100%; padding: 8px; margin-bottom: 10px; }
        button { padding: 10px 20px; background: #0066cc; color: white; border: none; cursor: pointer; }
        .result { margin: 20px 0; padding: 15px; border: 1px solid #ccc; background: #f9f9f9; }
        .success { border-color: green; background: #e8f5e8; }
        .error { border-color: red; background: #ffe8e8; }
    </style>
</head>
<body>
    <form id="form1" runat="server">
        <div class="test-container">
            <h1>🧪 Test Envoi Messages & Pièces Jointes</h1>
            
            <div class="form-group">
                <label>Message :</label>
                <textarea id="txtMessage" runat="server" rows="3" placeholder="Tapez votre message ici... Vous pouvez utiliser :smile: :heart: etc."></textarea>
            </div>
            
            <div class="form-group">
                <label>Destinataire ID :</label>
                <input type="number" id="txtDestinataire" runat="server" value="2" />
            </div>
            
            <div class="form-group">
                <label>Pièce jointe (optionnel) :</label>
                <input type="file" id="fileUpload" runat="server" />
            </div>
            
            <div class="form-group">
                <button type="button" runat="server" id="btnEnvoyer" onserverclick="btnEnvoyer_Click">
                    📤 Envoyer Message
                </button>
            </div>
            
            <div class="result" id="resultDiv" runat="server" visible="false">
                <asp:Label ID="lblResult" runat="server"></asp:Label>
            </div>
            
            <!-- Champs cachés pour simulation -->
            <input type="hidden" id="hdnAttachmentPath" runat="server" />
            <input type="hidden" id="lblId" runat="server" />
        </div>
    </form>
    
    <script>
        // Simuler le comportement de la messagerie
        document.addEventListener('DOMContentLoaded', function() {
            const txtDestinataire = document.getElementById('<%= txtDestinataire.ClientID %>');
            const lblId = document.getElementById('<%= lblId.ClientID %>');
            
            // Synchroniser les valeurs
            txtDestinataire.addEventListener('change', function() {
                lblId.value = this.value;
            });
            
            // Initialiser
            lblId.value = txtDestinataire.value;
        });
    </script>
</body>
</html>
