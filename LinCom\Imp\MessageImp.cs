﻿using LinCom.Class;
using LinCom.Classe;
using LinCom.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI.WebControls;

namespace LinCom.Imp
{
    public class MessageImp : IMessage
    {
        int msg;
        private Message message = new Message();
        private MessageStatu mesast= new MessageStatu();

        public void AfficherDetails(long messageId, Message_Class messageClass)
        {
            using (Connection con = new Connection())
            {
                var m = con.Messages.FirstOrDefault(x => x.MessageId == messageId);
                if (m != null)
                {
                    messageClass.MessageId = m.MessageId;
                    messageClass.ConversationId = m.ConversationId;
                    messageClass.SenderId = m.SenderId;
                    messageClass.Contenu = m.Contenu;
                    messageClass.AttachmentUrl = m.AttachmentUrl;
                    messageClass.DateEnvoi = m.DateEnvoi;
                    messageClass.name = m.name;

    }
            }
        }

      

        public void ChargerMessages(Repeater rpt, long conversationId, int nombreMessages)
        {
            using (Connection con = new Connection())
            {
                try
                {
                    var messages = from m in con.Messages
                                   join mb in con.Membres on m.SenderId equals mb.MembreId
                                   where m.ConversationId == conversationId
                                   orderby m.DateEnvoi ascending // Ordre chronologique pour l'affichage
                                   select new
                                   {
                                       id = m.MessageId,
                                       Contenu = m.Contenu ?? "",
                                       Expediteur = (mb.Nom ?? "") + " " + (mb.Prenom ?? ""),
                                       Photomembre = mb.PhotoProfil ?? "default-avatar.png",
                                       DateEnvoi = m.DateEnvoi,
                                       name = m.name ?? "",
                                       AttachmentUrl = m.AttachmentUrl ?? "",
                                       SenderId = m.SenderId
                                   };

                    var messagesList = messages.Take(nombreMessages).ToList();

                    // Vérifier s'il y a des messages
                    if (messagesList.Any())
                    {
                        rpt.DataSource = messagesList;
                        rpt.DataBind();
                    }
                    else
                    {
                        // Aucun message, vider le repeater
                        rpt.DataSource = null;
                        rpt.DataBind();
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"Erreur lors du chargement des messages: {ex.Message}");
                    rpt.DataSource = null;
                    rpt.DataBind();
                }
            }
        }

        public int CompterNonLus(long membreId)
        {
            using (Connection con = new Connection())
            {
                return con.Messages
                    .Count(m => m.SenderId != membreId &&
                               con.ParticipantConversations.Any(p =>
                                   p.ConversationId == m.ConversationId &&
                                   p.MembreId == membreId));
            }
        }

        public int Envoyer(Message_Class messageClass)
        {
            using (Connection con = new Connection())
            {
                // Créer une nouvelle instance pour éviter les réutilisations
                var nouveauMessage = new Message
                {
                    ConversationId = messageClass.ConversationId,
                    SenderId = messageClass.SenderId,
                    Contenu = messageClass.Contenu,
                    DateEnvoi = DateTime.Now,
                    name = messageClass.name,
                    AttachmentUrl = messageClass.AttachmentUrl
                };

                try
                {
                    con.Messages.Add(nouveauMessage);
                    int result = con.SaveChanges();

                    // Mettre à jour l'ID du message dans la classe pour référence future
                    messageClass.MessageId = nouveauMessage.MessageId;

                    return result;
                }
                catch (Exception ex)
                {
                    // Log l'erreur pour le débogage
                    System.Diagnostics.Debug.WriteLine($"Erreur lors de l'envoi du message: {ex.Message}");
                    return 0;
                }
            }
        }

        // Méthode pour vérifier si un message similaire existe déjà (éviter les doublons)
        private bool MessageExisteDeja(long conversationId, long senderId, string contenu, int minutesTolerance = 1)
        {
            using (Connection con = new Connection())
            {
                var dateLimit = DateTime.Now.AddMinutes(-minutesTolerance);

                return con.Messages.Any(m =>
                    m.ConversationId == conversationId &&
                    m.SenderId == senderId &&
                    m.Contenu == contenu &&
                    m.DateEnvoi >= dateLimit);
            }
        }

        // Nouvelle méthode pour envoyer un message avec gestion complète des statuts
        public int EnvoyerMessageComplet(Message_Class messageClass, long[] participantIds)
        {
            using (var con = new Connection())
            {
                using (var transaction = con.Database.BeginTransaction())
                {
                    try
                    {
                        // Vérifier les doublons
                        if (messageClass.ConversationId.HasValue && messageClass.SenderId.HasValue &&
                            MessageExisteDeja(messageClass.ConversationId.Value, messageClass.SenderId.Value, messageClass.Contenu))
                        {
                            System.Diagnostics.Debug.WriteLine("Message en doublon détecté, envoi annulé");
                            return 0;
                        }

                        // 1. Créer le message
                        var nouveauMessage = new Message
                        {
                            ConversationId = messageClass.ConversationId,
                            SenderId = messageClass.SenderId,
                            Contenu = messageClass.Contenu,
                            DateEnvoi = DateTime.Now,
                            name = messageClass.name,
                            AttachmentUrl = messageClass.AttachmentUrl
                        };

                        con.Messages.Add(nouveauMessage);
                        con.SaveChanges(); // Génère l'ID du message

                        // 2. Créer les statuts pour tous les participants
                        foreach (var participantId in participantIds)
                        {
                            var status = new MessageStatu
                            {
                                MessageId = nouveauMessage.MessageId,
                                UserId = participantId,
                                IsRead = participantId == messageClass.SenderId ? 1 : 0,
                                ReadAt = participantId == messageClass.SenderId ? (DateTime?)DateTime.Now : null
                            };
                            con.MessageStatus.Add(status);
                        }

                        con.SaveChanges();
                        transaction.Commit();

                        messageClass.MessageId = nouveauMessage.MessageId;
                        return 1;
                    }
                    catch (Exception ex)
                    {
                        transaction.Rollback();
                        System.Diagnostics.Debug.WriteLine($"Erreur lors de l'envoi complet: {ex.Message}");
                        return 0;
                    }
                }
            }
        }

        public int EnvoyerMessageStatus(MessageStatus_Class messageClass)
        {
            using (Connection con = new Connection())
            {
                // Créer une nouvelle instance pour éviter les réutilisations
                var nouveauStatus = new MessageStatu
                {
                    MessageId = messageClass.MessageId,
                    UserId = messageClass.UserId,
                    IsRead = messageClass.IsRead,
                    ReadAt = messageClass.IsRead == 1 ? DateTime.Now : (DateTime?)null
                };

                try
                {
                    con.MessageStatus.Add(nouveauStatus);
                    return con.SaveChanges();
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"Erreur lors de la création du statut: {ex.Message}");
                    return 0;
                }
            }
        }



        public int Modifier(Message_Class messageClass)
        {
            using (Connection con = new Connection())
            {
                var m = con.Messages.FirstOrDefault(x => x.MessageId == messageClass.MessageId);
                if (m != null)
                {
                    m.Contenu = messageClass.Contenu;
                    m.name = messageClass.name;

                    try
                    {
                        return con.SaveChanges();
                    }
                    catch
                    {
                        return 0;
                    }
                }
                return 0;
            }
        }

        public int Supprimer(long messageId)
        {
            using (Connection con = new Connection())
            {
                var m = con.Messages.FirstOrDefault(x => x.MessageId == messageId);
                if (m != null)
                {
                    con.Messages.Remove(m);
                    try
                    {
                        return con.SaveChanges();
                    }
                    catch
                    {
                        return 0;
                    }
                }
                return 0;
            }
        }


        //Methodes pour Messages Statut

        public void AfficherDetailsMessageStatut(long statusId, MessageStatus_Class statusClass)
        {
            using (Connection con = new Connection())
            {
                var status = con.MessageStatus.FirstOrDefault(x => x.MessagestatusID == statusId);
                if (status != null)
                {
                    statusClass.MessagestatusID = status.MessagestatusID;
                    statusClass.MessageId = status.MessageId;
                    statusClass.UserId = status.UserId;
                    statusClass.IsRead = status.IsRead;
                    statusClass.ReadAt = status.ReadAt;
                }
            }
        }

        public int MarquerCommeLu(long messageId, long userId)
        {
            using (Connection con = new Connection())
            {
                var status = con.MessageStatus.FirstOrDefault(x => x.MessageId == messageId && x.UserId == userId);
                if (status != null)
                {
                    status.IsRead = 1;
                    status.ReadAt = DateTime.Now;
                    return con.SaveChanges();
                }
                return 0;
            }
        }

        // Nouvelle méthode pour marquer tous les messages d'une conversation comme lus
        public int MarquerConversationCommeLue(long conversationId, long userId)
        {
            using (Connection con = new Connection())
            {
                try
                {
                    var messagesNonLus = from ms in con.MessageStatus
                                        join m in con.Messages on ms.MessageId equals m.MessageId
                                        where m.ConversationId == conversationId
                                              && ms.UserId == userId
                                              && ms.IsRead == 0
                                        select ms;

                    foreach (var status in messagesNonLus)
                    {
                        status.IsRead = 1;
                        status.ReadAt = DateTime.Now;
                    }

                    return con.SaveChanges();
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"Erreur lors du marquage comme lu: {ex.Message}");
                    return 0;
                }
            }
        }

        public int AjouterMessageEtStatusPourTous(long conversationId, long senderId, string contenu, string attachmentUrl = null)
        {
            using (var con = new Connection())
            {
                using (var transaction = con.Database.BeginTransaction())
                {
                    try
                    {
                        // 1. Création et ajout du message
                        var message = new Message
                        {
                            ConversationId = conversationId,
                            SenderId = senderId,
                            Contenu = contenu,
                            AttachmentUrl = attachmentUrl,
                            DateEnvoi = DateTime.Now,
                            name = "Nom ou pseudo de l'expéditeur" // adapte selon ton contexte
                        };

                        con.Messages.Add(message);
                        con.SaveChanges(); // Génère MessageId

                        // 2. Récupérer tous les participants de la conversation
                        var participants = con.ParticipantConversations
                                              .Where(pc => pc.ConversationId == conversationId)
                                              .Select(pc => pc.MembreId)
                                              .ToList();

                        // 3. Créer les MessageStatus pour tous
                        foreach (var membreId in participants)
                        {
                            var status = new MessageStatu
                            {
                                MessageId = message.MessageId,
                                UserId = (long)membreId,
                                IsRead = (membreId == senderId) ? 1 : 0,
                                ReadAt = (membreId == senderId) ? (DateTime?)DateTime.Now : null
                            };
                            con.MessageStatus.Add(status);
                        }

                        con.SaveChanges();
                        transaction.Commit();

                        return 1; // succès
                    }
                    catch
                    {
                        transaction.Rollback();
                        return 0; // échec
                    }
                }
            }
        }


        public int SupprimerMessageStatut(long messageStatusId)
        {
            using (Connection con = new Connection())
            {
                var status = con.MessageStatus.FirstOrDefault(x => x.MessagestatusID == messageStatusId);
                if (status != null)
                {
                    con.MessageStatus.Remove(status);
                    return con.SaveChanges();
                }
                return 0;
            }
        }

        #region Nouvelles méthodes pour l'aperçu LinkedIn-style

        /// <summary>
        /// Récupère le dernier message entre deux utilisateurs
        /// </summary>
        public Message_Class GetLastMessageBetweenUsers(long userId1, long userId2)
        {
            using (Connection con = new Connection())
            {
                try
                {
                    // Trouver la conversation entre les deux utilisateurs
                    var conversation = (from c in con.Conversations
                                      join p1 in con.ParticipantConversations on c.ConversationId equals p1.ConversationId
                                      join p2 in con.ParticipantConversations on c.ConversationId equals p2.ConversationId
                                      where p1.MembreId == userId1 && p2.MembreId == userId2 && p1.MembreId != p2.MembreId
                                      select c).FirstOrDefault();

                    if (conversation == null) return null;

                    // Récupérer le dernier message de cette conversation
                    var lastMessage = con.Messages
                        .Where(m => m.ConversationId == conversation.ConversationId)
                        .OrderByDescending(m => m.DateEnvoi)
                        .FirstOrDefault();

                    if (lastMessage != null)
                    {
                        return new Message_Class
                        {
                            MessageId = lastMessage.MessageId,
                            ConversationId = lastMessage.ConversationId,
                            SenderId = lastMessage.SenderId,
                            Contenu = lastMessage.Contenu,
                            AttachmentUrl = lastMessage.AttachmentUrl,
                            DateEnvoi = lastMessage.DateEnvoi,
                            name = lastMessage.name
                        };
                    }

                    return null;
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"Erreur GetLastMessageBetweenUsers: {ex.Message}");
                    return null;
                }
            }
        }

        /// <summary>
        /// Compte les messages non lus d'un utilisateur spécifique
        /// </summary>
        public int GetUnreadMessageCount(long userId, long fromUserId)
        {
            using (Connection con = new Connection())
            {
                try
                {
                    // Trouver la conversation entre les deux utilisateurs
                    var conversation = (from c in con.Conversations
                                      join p1 in con.ParticipantConversations on c.ConversationId equals p1.ConversationId
                                      join p2 in con.ParticipantConversations on c.ConversationId equals p2.ConversationId
                                      where p1.MembreId == userId && p2.MembreId == fromUserId && p1.MembreId != p2.MembreId
                                      select c).FirstOrDefault();

                    if (conversation == null) return 0;

                    // Compter les messages non lus de fromUserId dans cette conversation
                    var unreadCount = (from m in con.Messages
                                     join ms in con.MessageStatus on m.MessageId equals ms.MessageId
                                     where m.ConversationId == conversation.ConversationId
                                           && m.SenderId == fromUserId
                                           && ms.UserId == userId
                                           && ms.IsRead == 0
                                     select m).Count();

                    return unreadCount;
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"Erreur GetUnreadMessageCount: {ex.Message}");
                    return 0;
                }
            }
        }

        /// <summary>
        /// Récupère le dernier message envoyé par un utilisateur à un autre
        /// </summary>
        public Message_Class GetLastSentMessageToUser(long senderId, long receiverId)
        {
            using (Connection con = new Connection())
            {
                try
                {
                    // Trouver la conversation entre les deux utilisateurs
                    var conversation = (from c in con.Conversations
                                      join p1 in con.ParticipantConversations on c.ConversationId equals p1.ConversationId
                                      join p2 in con.ParticipantConversations on c.ConversationId equals p2.ConversationId
                                      where p1.MembreId == senderId && p2.MembreId == receiverId && p1.MembreId != p2.MembreId
                                      select c).FirstOrDefault();

                    if (conversation == null) return null;

                    // Récupérer le dernier message envoyé par senderId dans cette conversation
                    var lastSentMessage = con.Messages
                        .Where(m => m.ConversationId == conversation.ConversationId && m.SenderId == senderId)
                        .OrderByDescending(m => m.DateEnvoi)
                        .FirstOrDefault();

                    if (lastSentMessage != null)
                    {
                        return new Message_Class
                        {
                            MessageId = lastSentMessage.MessageId,
                            ConversationId = lastSentMessage.ConversationId,
                            SenderId = lastSentMessage.SenderId,
                            Contenu = lastSentMessage.Contenu,
                            AttachmentUrl = lastSentMessage.AttachmentUrl,
                            DateEnvoi = lastSentMessage.DateEnvoi,
                            name = lastSentMessage.name
                        };
                    }

                    return null;
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"Erreur GetLastSentMessageToUser: {ex.Message}");
                    return null;
                }
            }
        }

        /// <summary>
        /// Récupère le statut d'un message
        /// </summary>
        public MessageStatus_Class GetMessageStatus(long messageId)
        {
            using (Connection con = new Connection())
            {
                try
                {
                    var status = con.MessageStatus.FirstOrDefault(ms => ms.MessageId == messageId);

                    if (status != null)
                    {
                        return new MessageStatus_Class
                        {
                            MessagestatusID = status.MessagestatusID,
                            MessageId = status.MessageId,
                            UserId = status.UserId,
                            IsRead = status.IsRead,
                            ReadAt = status.ReadAt,
                            Statut = status.IsRead == 1 ? "lu" : "delivre"
                        };
                    }

                    return null;
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"Erreur GetMessageStatus: {ex.Message}");
                    return null;
                }
            }
        }

        #endregion

    }
}