/* Messagerie Style LinkedIn - Fichier CSS séparé */

/* Variables CSS pour cohérence */
:root {
    --linkedin-blue: #0a66c2;
    --linkedin-light-blue: #e7f3ff;
    --linkedin-dark-blue: #004182;
    --linkedin-gray: #666666;
    --linkedin-light-gray: #f3f2ef;
    --linkedin-border: #e0e0e0;
    --linkedin-white: #ffffff;
    --linkedin-green: #057642;
    --linkedin-hover: #f5f5f5;
    --message-sent: #0a66c2;
    --message-received: #f3f2ef;
    --shadow-light: 0 2px 4px rgba(0,0,0,0.08);
    --shadow-medium: 0 4px 12px rgba(0,0,0,0.15);
}

/* Styles de base */
body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    background-color: var(--linkedin-light-gray);
    margin: 0;
    padding: 0;
}

/* Container principal */
.chat-wrapper {
    display: flex;
    height: 85vh;
    max-height: 800px;
    border-radius: 8px;
    box-shadow: var(--shadow-medium);
    overflow: hidden;
    background: var(--linkedin-white);
    border: 1px solid var(--linkedin-border);
}

/* Panel de contacts */
.contacts-panel {
    width: 320px;
    background: var(--linkedin-white);
    border-right: 1px solid var(--linkedin-border);
    overflow-y: auto;
    display: flex;
    flex-direction: column;
}

.contacts-header {
    background: var(--linkedin-white);
    color: var(--linkedin-gray);
    padding: 16px 20px;
    font-weight: 600;
    font-size: 16px;
    border-bottom: 1px solid var(--linkedin-border);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.contacts-search {
    padding: 16px 20px;
    border-bottom: 1px solid var(--linkedin-border);
    background: var(--linkedin-white);
}

.contacts-search input {
    width: 100%;
    padding: 8px 12px;
    border-radius: 4px;
    border: 1px solid var(--linkedin-border);
    font-size: 14px;
    background-color: var(--linkedin-light-gray);
    transition: all 0.2s ease;
}

.contacts-search input:focus {
    outline: none;
    border-color: var(--linkedin-blue);
    background-color: var(--linkedin-white);
    box-shadow: 0 0 0 2px var(--linkedin-light-blue);
}

/* Items de contact */
.contact-item {
    padding: 12px 20px;
    display: flex;
    align-items: center;
    gap: 12px;
    cursor: pointer;
    transition: background-color 0.2s ease;
    border: none;
    background: none;
    text-decoration: none;
    color: inherit;
    position: relative;
}

.contact-item:hover {
    background-color: var(--linkedin-hover);
}

.contact-item.active {
    background-color: var(--linkedin-light-blue);
    border-right: 2px solid var(--linkedin-blue);
}

.contact-item img {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid var(--linkedin-border);
}

.contact-info {
    flex: 1;
    min-width: 0;
}

.contact-name {
    font-weight: 600;
    font-size: 14px;
    color: #000;
    margin-bottom: 2px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.contact-status {
    font-size: 12px;
    color: var(--linkedin-gray);
    display: flex;
    align-items: center;
    gap: 4px;
}

.online-indicator {
    width: 8px;
    height: 8px;
    background-color: var(--linkedin-green);
    border-radius: 50%;
    border: 1px solid var(--linkedin-white);
}

/* Panel de chat principal */
.chat-panel {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: var(--linkedin-white);
    min-width: 0;
}

.chat-header {
    background: var(--linkedin-white);
    padding: 16px 24px;
    border-bottom: 1px solid var(--linkedin-border);
    display: flex;
    align-items: center;
    gap: 12px;
}

.chat-header-info {
    flex: 1;
}

.chat-header-name {
    font-weight: 600;
    font-size: 16px;
    color: #000;
    margin-bottom: 2px;
}

.chat-header-status {
    font-size: 12px;
    color: var(--linkedin-gray);
}

.chat-body {
    flex: 1;
    padding: 16px 24px;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: 12px;
    background: var(--linkedin-white);
}

/* Messages */
.message-container {
    margin-bottom: 16px;
    max-width: 70%;
    display: flex;
    flex-direction: column;
    position: relative;
    animation: fadeInUp 0.3s ease-out;
}

.message-container.sent {
    align-self: flex-end;
    align-items: flex-end;
}

.message-container.received {
    align-self: flex-start;
    align-items: flex-start;
}

.message-header {
    display: flex;
    align-items: center;
    margin-bottom: 4px;
    gap: 8px;
}

.message-container.sent .message-header {
    flex-direction: row-reverse;
}

.message-header .avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    object-fit: cover;
    border: 1px solid var(--linkedin-border);
}

.message-sender {
    font-size: 12px;
    font-weight: 600;
    color: var(--linkedin-gray);
}

.message-time {
    font-size: 11px;
    color: var(--linkedin-gray);
    margin-left: 8px;
}

.message-bubble {
    padding: 12px 16px;
    border-radius: 18px;
    font-size: 14px;
    line-height: 1.4;
    position: relative;
    word-wrap: break-word;
    max-width: 100%;
}

.message-container.sent .message-bubble {
    background: var(--message-sent);
    color: var(--linkedin-white);
    border-bottom-right-radius: 4px;
}

.message-container.received .message-bubble {
    background: var(--message-received);
    color: #000;
    border: 1px solid var(--linkedin-border);
    border-bottom-left-radius: 4px;
}

.message-body p {
    margin: 0;
    font-size: 14px;
    line-height: 1.4;
}

.message-footer {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    margin-top: 4px;
    gap: 4px;
}

.message-container.received .message-footer {
    justify-content: flex-start;
}

/* Zone de saisie */
.chat-footer {
    padding: 16px 24px;
    background: var(--linkedin-white);
    border-top: 1px solid var(--linkedin-border);
}

.message-input-container {
    display: flex;
    align-items: flex-end;
    gap: 12px;
    position: relative;
    background: var(--linkedin-white);
    border: 1px solid var(--linkedin-border);
    border-radius: 24px;
    padding: 8px 16px;
    transition: all 0.2s ease;
}

.message-input-container:focus-within {
    border-color: var(--linkedin-blue);
    box-shadow: 0 0 0 2px var(--linkedin-light-blue);
}

.input-toolbar {
    display: flex;
    align-items: center;
    gap: 8px;
}

.toolbar-btn {
    background: transparent;
    border: none;
    border-radius: 50%;
    width: 32px;
    height: 32px;
    cursor: pointer;
    font-size: 16px;
    color: var(--linkedin-gray);
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.toolbar-btn:hover {
    background: var(--linkedin-hover);
    color: var(--linkedin-blue);
}

#txtMessage {
    flex: 1;
    border: none;
    outline: none;
    resize: none;
    font-size: 14px;
    line-height: 1.4;
    padding: 8px 0;
    background: transparent;
    font-family: inherit;
    min-height: 20px;
    max-height: 120px;
}

.send-btn {
    background: var(--linkedin-blue);
    color: var(--linkedin-white);
    border: none;
    border-radius: 50%;
    width: 36px;
    height: 36px;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
}

.send-btn:hover {
    background: var(--linkedin-dark-blue);
    transform: scale(1.05);
}

.send-btn:disabled {
    background: var(--linkedin-border);
    cursor: not-allowed;
    transform: none;
}
