/* Messagerie Style LinkedIn - Fichier CSS séparé */

/* Variables CSS pour cohérence */
:root {
    --linkedin-blue: #0a66c2;
    --linkedin-light-blue: #e7f3ff;
    --linkedin-dark-blue: #004182;
    --linkedin-gray: #666666;
    --linkedin-light-gray: #f3f2ef;
    --linkedin-border: #e0e0e0;
    --linkedin-white: #ffffff;
    --linkedin-green: #057642;
    --linkedin-hover: #f5f5f5;
    --message-sent: #0a66c2;
    --message-received: #f3f2ef;
    --shadow-light: 0 2px 4px rgba(0,0,0,0.08);
    --shadow-medium: 0 4px 12px rgba(0,0,0,0.15);
}

/* Styles de base */
body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    background-color: var(--linkedin-light-gray);
    margin: 0;
    padding: 0;
}

/* Container principal */
.chat-wrapper {
    display: flex;
    height: 85vh;
    max-height: 800px;
    border-radius: 8px;
    box-shadow: var(--shadow-medium);
    overflow: hidden;
    background: var(--linkedin-white);
    border: 1px solid var(--linkedin-border);
}

/* Panel de contacts */
.contacts-panel {
    width: 320px;
    background: var(--linkedin-white);
    border-right: 1px solid var(--linkedin-border);
    display: flex;
    flex-direction: column;
    overflow: hidden; /* Empêche le scroll sur le panel entier */
}

/* En-tête Messages LinkedIn style - STICKY */
.linkedin-messages-header {
    background: var(--linkedin-white);
    border-bottom: 1px solid var(--linkedin-border);
    padding: 0;
    position: sticky;
    top: 0;
    z-index: 10;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.messages-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 20px 12px;
}

.title-main {
    display: flex;
    align-items: center;
    gap: 8px;
}

.messages-icon {
    color: var(--linkedin-gray);
}

.title-text {
    font-size: 20px;
    font-weight: 600;
    color: #000;
}

.messages-actions {
    display: flex;
    align-items: center;
    gap: 4px;
}

.header-action-btn {
    background: transparent;
    border: none;
    border-radius: 50%;
    width: 32px;
    height: 32px;
    cursor: pointer;
    color: var(--linkedin-gray);
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.header-action-btn:hover {
    background: var(--linkedin-hover);
    color: var(--linkedin-blue);
}

/* Onglets Messages/Conversations */
.messages-tabs {
    display: flex;
    padding: 0 20px;
    border-bottom: 1px solid var(--linkedin-border);
}

.tab-btn {
    background: transparent;
    border: none;
    padding: 12px 0;
    margin-right: 24px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 600;
    color: var(--linkedin-gray);
    border-bottom: 2px solid transparent;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 6px;
}

.tab-btn:hover {
    color: #000;
}

.tab-btn.active {
    color: var(--linkedin-blue);
    border-bottom-color: var(--linkedin-blue);
}

.tab-count {
    background: var(--linkedin-light-gray);
    color: var(--linkedin-gray);
    font-size: 11px;
    font-weight: 600;
    padding: 2px 6px;
    border-radius: 10px;
    min-width: 16px;
    text-align: center;
}

.tab-btn.active .tab-count {
    background: var(--linkedin-light-blue);
    color: var(--linkedin-blue);
}

/* Barre de recherche LinkedIn style - STICKY */
.linkedin-search-container {
    padding: 16px 20px;
    background: var(--linkedin-white);
    border-bottom: 1px solid var(--linkedin-border);
    position: sticky;
    top: 0;
    z-index: 9;
    box-shadow: 0 1px 3px rgba(0,0,0,0.05);
}

.search-input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    background: var(--linkedin-light-gray);
    border: 1px solid var(--linkedin-border);
    border-radius: 4px;
    transition: all 0.2s ease;
}

.search-input-wrapper:focus-within {
    background: var(--linkedin-white);
    border-color: var(--linkedin-blue);
    box-shadow: 0 0 0 2px var(--linkedin-light-blue);
}

.search-icon {
    padding: 8px 12px;
    color: var(--linkedin-gray);
    display: flex;
    align-items: center;
}

.linkedin-search-input {
    flex: 1;
    border: none;
    outline: none;
    background: transparent;
    padding: 8px 4px 8px 0;
    font-size: 14px;
    color: #000;
    font-family: inherit;
}

.linkedin-search-input::placeholder {
    color: var(--linkedin-gray);
}

.search-actions {
    display: flex;
    align-items: center;
    padding: 4px 8px;
    gap: 4px;
}

.search-filter-btn,
.search-clear-btn {
    background: transparent;
    border: none;
    border-radius: 4px;
    width: 24px;
    height: 24px;
    cursor: pointer;
    color: var(--linkedin-gray);
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.search-filter-btn:hover,
.search-clear-btn:hover {
    background: var(--linkedin-hover);
    color: var(--linkedin-blue);
}

/* Suggestions de recherche */
.search-suggestions {
    position: absolute;
    top: 100%;
    left: 20px;
    right: 20px;
    background: var(--linkedin-white);
    border: 1px solid var(--linkedin-border);
    border-radius: 8px;
    box-shadow: var(--shadow-medium);
    z-index: 1000;
    max-height: 300px;
    overflow-y: auto;
}

.suggestions-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    border-bottom: 1px solid var(--linkedin-border);
    font-size: 12px;
    font-weight: 600;
    color: var(--linkedin-gray);
}

.clear-history-btn {
    background: transparent;
    border: none;
    color: var(--linkedin-blue);
    cursor: pointer;
    font-size: 12px;
    font-weight: 600;
    padding: 4px 8px;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.clear-history-btn:hover {
    background: var(--linkedin-light-blue);
}

.suggestions-list {
    padding: 8px 0;
}

.suggestion-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 8px 16px;
    cursor: pointer;
    transition: background 0.2s ease;
    font-size: 14px;
    color: #000;
}

.suggestion-item:hover {
    background: var(--linkedin-hover);
}

.suggestion-item svg {
    color: var(--linkedin-gray);
    flex-shrink: 0;
}

/* Liste des contacts scrollable */
.contacts-list {
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
}

/* Amélioration du sticky header quand on scroll */
.linkedin-messages-header {
    transition: box-shadow 0.2s ease;
}

.linkedin-messages-header.scrolled {
    box-shadow: 0 2px 8px rgba(0,0,0,0.12);
}

.linkedin-search-container {
    transition: box-shadow 0.2s ease;
}

.linkedin-search-container.scrolled {
    box-shadow: 0 2px 6px rgba(0,0,0,0.1);
}

/* Scrollbar personnalisée pour la liste des contacts */
.contacts-list::-webkit-scrollbar {
    width: 6px;
}

.contacts-list::-webkit-scrollbar-track {
    background: transparent;
}

.contacts-list::-webkit-scrollbar-thumb {
    background: var(--linkedin-border);
    border-radius: 3px;
    transition: background 0.2s ease;
}

.contacts-list::-webkit-scrollbar-thumb:hover {
    background: var(--linkedin-gray);
}

/* Effet de fade pour les éléments en haut/bas lors du scroll */
.contacts-list {
    position: relative;
}

.contacts-list::before {
    content: '';
    position: sticky;
    top: 0;
    height: 10px;
    background: linear-gradient(to bottom, var(--linkedin-white), transparent);
    z-index: 1;
    pointer-events: none;
}

.contacts-list::after {
    content: '';
    position: sticky;
    bottom: 0;
    height: 10px;
    background: linear-gradient(to top, var(--linkedin-white), transparent);
    z-index: 1;
    pointer-events: none;
}

/* Items de contact LinkedIn style avec aperçu des messages */
.linkedin-contact-item {
    padding: 16px 20px;
    display: flex;
    align-items: flex-start;
    gap: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
    border: none;
    background: var(--linkedin-white);
    text-decoration: none;
    color: inherit;
    position: relative;
    width: 100%;
    text-align: left;
    border-bottom: 1px solid #f0f0f0;
}

.linkedin-contact-item:hover {
    background-color: #f8f9fa;
}

.linkedin-contact-item.active {
    background-color: #e7f3ff;
    border-left: 3px solid var(--linkedin-blue);
}

/* Avatar */
.contact-avatar-container {
    position: relative;
    flex-shrink: 0;
}

.contact-avatar {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    object-fit: cover;
    border: none;
}

/* Informations principales du contact */
.contact-main-info {
    flex: 1;
    min-width: 0;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.contact-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 4px;
}

.contact-name {
    font-weight: 600;
    font-size: 16px;
    color: #000;
    line-height: 1.3;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    flex: 1;
    margin-right: 8px;
}

.message-time {
    font-size: 12px;
    color: #666;
    font-weight: 400;
    white-space: nowrap;
    flex-shrink: 0;
}

/* Aperçu du message */
.contact-preview {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 8px;
}

.last-message {
    flex: 1;
    font-size: 14px;
    color: #666;
    line-height: 1.3;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    margin-right: 8px;
    display: flex;
    align-items: center;
}

.message-prefix {
    color: #666;
    margin-right: 4px;
    font-weight: 500;
}

.message-content {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
}

.last-message.unread {
    color: #000;
    font-weight: 600;
}

.last-message.unread .message-content {
    color: #000;
    font-weight: 600;
}

/* Indicateurs de message */
.message-indicators {
    display: flex;
    align-items: center;
    gap: 6px;
    flex-shrink: 0;
}

.notification-count {
    background: #0a66c2;
    color: white;
    font-size: 11px;
    font-weight: 600;
    padding: 2px 6px;
    border-radius: 50%;
    min-width: 18px;
    height: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    line-height: 1;
    box-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

/* États spéciaux */
.linkedin-contact-item.has-unread {
    background: linear-gradient(90deg, transparent 0%, var(--linkedin-light-blue) 100%);
}

.linkedin-contact-item.has-unread .contact-name,
.linkedin-contact-item.has-unread .last-message {
    font-weight: 600;
    color: #000;
}

.linkedin-contact-item.muted {
    opacity: 0.6;
}

.linkedin-contact-item.muted .last-message {
    text-decoration: line-through;
}

/* Panel de chat principal */
.chat-panel {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: var(--linkedin-white);
    min-width: 0;
}

.chat-header {
    background: var(--linkedin-white);
    padding: 16px 24px;
    border-bottom: 1px solid var(--linkedin-border);
    display: flex;
    align-items: center;
    gap: 12px;
}

.chat-header-info {
    flex: 1;
}

.chat-header-name {
    font-weight: 600;
    font-size: 16px;
    color: #000;
    margin-bottom: 2px;
}

.chat-header-status {
    font-size: 12px;
    color: var(--linkedin-gray);
}

.chat-body {
    flex: 1;
    padding: 16px 24px;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: 12px;
    background: var(--linkedin-white);
}

/* Messages */
.message-container {
    margin-bottom: 16px;
    max-width: 70%;
    display: flex;
    flex-direction: column;
    position: relative;
    animation: fadeInUp 0.3s ease-out;
}

.message-container.sent {
    align-self: flex-end;
    align-items: flex-end;
}

.message-container.received {
    align-self: flex-start;
    align-items: flex-start;
}

.message-header {
    display: flex;
    align-items: center;
    margin-bottom: 4px;
    gap: 8px;
}

.message-container.sent .message-header {
    flex-direction: row-reverse;
}

.message-header .avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    object-fit: cover;
    border: 1px solid var(--linkedin-border);
}

.message-sender {
    font-size: 12px;
    font-weight: 600;
    color: var(--linkedin-gray);
}

.message-time {
    font-size: 11px;
    color: var(--linkedin-gray);
    margin-left: 8px;
}

.message-bubble {
    padding: 12px 16px;
    border-radius: 18px;
    font-size: 14px;
    line-height: 1.4;
    position: relative;
    word-wrap: break-word;
    max-width: 100%;
}

.message-container.sent .message-bubble {
    background: var(--message-sent);
    color: var(--linkedin-white);
    border-bottom-right-radius: 4px;
}

.message-container.received .message-bubble {
    background: var(--message-received);
    color: #000;
    border: 1px solid var(--linkedin-border);
    border-bottom-left-radius: 4px;
}

.message-body p {
    margin: 0;
    font-size: 14px;
    line-height: 1.4;
}

.message-footer {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    margin-top: 4px;
    gap: 4px;
}

.message-container.received .message-footer {
    justify-content: flex-start;
}

/* Zone de saisie LinkedIn-style améliorée */
.chat-footer {
    padding: 0;
    background: var(--linkedin-white);
    border-top: 1px solid var(--linkedin-border);
}

.linkedin-message-composer {
    background: var(--linkedin-white);
    border-radius: 8px;
    margin: 16px;
    border: 1px solid var(--linkedin-border);
    transition: all 0.2s ease;
}

.linkedin-message-composer:focus-within {
    border-color: var(--linkedin-blue);
    box-shadow: 0 0 0 2px var(--linkedin-light-blue);
}

/* Barre d'outils supérieure */
.composer-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    border-bottom: 1px solid var(--linkedin-border);
    background: var(--linkedin-light-gray);
    border-radius: 8px 8px 0 0;
}

.toolbar-left,
.toolbar-right {
    display: flex;
    align-items: center;
    gap: 4px;
}

.format-btn {
    background: transparent;
    border: none;
    border-radius: 4px;
    width: 28px;
    height: 28px;
    cursor: pointer;
    color: var(--linkedin-gray);
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
}

.format-btn:hover {
    background: var(--linkedin-hover);
    color: var(--linkedin-blue);
}

.format-btn.active {
    background: var(--linkedin-light-blue);
    color: var(--linkedin-blue);
}

.toolbar-separator {
    width: 1px;
    height: 20px;
    background: var(--linkedin-border);
    margin: 0 4px;
}

/* Zone de saisie principale */
.message-input-wrapper {
    position: relative;
}

.input-container {
    display: flex;
    align-items: flex-end;
    padding: 12px;
    gap: 12px;
}

.message-input-area {
    flex: 1;
    position: relative;
    min-height: 40px;
}

.linkedin-message-input {
    width: 100%;
    border: none;
    outline: none;
    resize: none;
    font-size: 14px;
    line-height: 1.5;
    padding: 8px 12px;
    background: transparent;
    font-family: inherit;
    min-height: 24px;
    max-height: 120px;
    border-radius: 20px;
    transition: all 0.2s ease;
}

.linkedin-message-input:focus {
    background: var(--linkedin-light-gray);
}

.input-placeholder {
    position: absolute;
    top: 8px;
    left: 12px;
    color: var(--linkedin-gray);
    font-size: 14px;
    pointer-events: none;
    transition: all 0.2s ease;
    opacity: 1;
}

.input-placeholder.hidden {
    opacity: 0;
}

.send-button-container {
    display: flex;
    align-items: flex-end;
    padding-bottom: 2px;
}

.linkedin-send-btn {
    background: var(--linkedin-blue);
    color: var(--linkedin-white);
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 4px rgba(10, 102, 194, 0.2);
}

.linkedin-send-btn:hover:not(:disabled) {
    background: var(--linkedin-dark-blue);
    transform: scale(1.05);
    box-shadow: 0 4px 8px rgba(10, 102, 194, 0.3);
}

.linkedin-send-btn:disabled {
    background: var(--linkedin-border);
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

/* Suggestions et mentions */
.suggestions-container {
    position: absolute;
    bottom: 100%;
    left: 0;
    right: 0;
    background: var(--linkedin-white);
    border: 1px solid var(--linkedin-border);
    border-radius: 8px;
    box-shadow: var(--shadow-medium);
    max-height: 200px;
    overflow-y: auto;
    z-index: 1000;
}

.suggestions-list {
    padding: 8px 0;
}

.suggestion-item {
    padding: 8px 16px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 12px;
    transition: background 0.2s ease;
}

.suggestion-item:hover,
.suggestion-item.selected {
    background: var(--linkedin-hover);
}

.suggestion-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    object-fit: cover;
}

.suggestion-info {
    flex: 1;
}

.suggestion-name {
    font-weight: 600;
    font-size: 14px;
    color: #000;
}

.suggestion-title {
    font-size: 12px;
    color: var(--linkedin-gray);
}

/* Footer du composer */
.composer-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    border-top: 1px solid var(--linkedin-border);
    background: var(--linkedin-light-gray);
    border-radius: 0 0 8px 8px;
    font-size: 12px;
}

.footer-left {
    display: flex;
    align-items: center;
    gap: 12px;
}

.char-counter {
    color: var(--linkedin-gray);
    font-size: 11px;
}

.char-counter.warning {
    color: #f39c12;
}

.char-counter.danger {
    color: #e74c3c;
}

.typing-indicator {
    display: flex;
    align-items: center;
    gap: 6px;
    color: var(--linkedin-blue);
    font-size: 11px;
}

.typing-dots {
    display: flex;
    gap: 2px;
}

.typing-dots span {
    width: 4px;
    height: 4px;
    background: var(--linkedin-blue);
    border-radius: 50%;
    animation: typing 1.4s infinite ease-in-out;
}

.typing-dots span:nth-child(1) { animation-delay: -0.32s; }
.typing-dots span:nth-child(2) { animation-delay: -0.16s; }

@keyframes typing {
    0%, 80%, 100% {
        transform: scale(0);
        opacity: 0.5;
    }
    40% {
        transform: scale(1);
        opacity: 1;
    }
}

.schedule-btn {
    background: transparent;
    border: none;
    border-radius: 4px;
    width: 24px;
    height: 24px;
    cursor: pointer;
    color: var(--linkedin-gray);
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.schedule-btn:hover {
    background: var(--linkedin-hover);
    color: var(--linkedin-blue);
}

/* Responsive Design pour la zone de saisie LinkedIn */
@media (max-width: 768px) {
    .linkedin-message-composer {
        margin: 8px;
    }

    .composer-toolbar {
        padding: 6px 8px;
    }

    .toolbar-left,
    .toolbar-right {
        gap: 2px;
    }

    .format-btn {
        width: 24px;
        height: 24px;
    }

    .input-container {
        padding: 8px;
        gap: 8px;
    }

    .linkedin-send-btn {
        width: 36px;
        height: 36px;
    }

    .composer-footer {
        padding: 6px 8px;
    }
}

@media (max-width: 480px) {
    /* Mobile optimizations pour la zone de saisie */
    .linkedin-message-composer {
        margin: 4px;
        border-radius: 6px;
    }

    .composer-toolbar {
        flex-wrap: wrap;
        gap: 4px;
    }

    .toolbar-left {
        flex: 1;
        justify-content: flex-start;
    }

    .toolbar-right {
        justify-content: flex-end;
    }

    .format-btn {
        width: 32px;
        height: 32px;
        font-size: 14px;
    }

    .linkedin-message-input {
        font-size: 16px; /* Évite le zoom sur iOS */
        padding: 12px;
    }

    .linkedin-send-btn {
        width: 44px;
        height: 44px;
    }

    .suggestions-container {
        max-height: 150px;
    }

    .suggestion-item {
        padding: 12px 16px;
    }

    .suggestion-avatar {
        width: 40px;
        height: 40px;
    }
}

/* Animations et transitions supplémentaires */
.linkedin-message-composer.focused {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
}

.format-btn.active {
    animation: buttonPress 0.2s ease;
}

@keyframes buttonPress {
    0% { transform: scale(1); }
    50% { transform: scale(0.95); }
    100% { transform: scale(1); }
}

/* États de chargement */
.linkedin-send-btn.sending {
    animation: sending 1s infinite;
}

@keyframes sending {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.6; }
}

/* Amélioration de l'accessibilité */
.format-btn:focus,
.linkedin-send-btn:focus,
.schedule-btn:focus {
    outline: 2px solid var(--linkedin-blue);
    outline-offset: 2px;
}

.suggestion-item:focus {
    background: var(--linkedin-light-blue);
    outline: 2px solid var(--linkedin-blue);
    outline-offset: -2px;
}
