<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="debug-envoi.aspx.cs" Inherits="LinCom.debug_envoi" %>

<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <title>Debug Envoi Messages</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .section h3 { margin-top: 0; color: #333; }
        .form-group { margin: 10px 0; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input, textarea, select { width: 100%; padding: 8px; margin-bottom: 10px; border: 1px solid #ccc; border-radius: 4px; }
        button { padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; margin: 5px; }
        button:hover { background: #0056b3; }
        .result { margin: 10px 0; padding: 10px; border-radius: 4px; }
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .info { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        .debug-log { background: #f8f9fa; border: 1px solid #dee2e6; padding: 10px; font-family: monospace; font-size: 12px; max-height: 300px; overflow-y: auto; }
    </style>
</head>
<body>
    <form id="form1" runat="server">
        <div class="container">
            <h1>🔧 Debug Envoi Messages & Pièces Jointes</h1>
            
            <!-- Section 1: Test Message Simple -->
            <div class="section">
                <h3>1. Test Message Simple</h3>
                <div class="form-group">
                    <label>Message :</label>
                    <textarea id="txtMessage" runat="server" rows="3" placeholder="Tapez votre message ici... Vous pouvez utiliser :smile: :heart: etc."></textarea>
                </div>
                <div class="form-group">
                    <label>Destinataire ID :</label>
                    <input type="number" id="txtDestinataire" runat="server" value="2" />
                </div>
                <button type="button" runat="server" id="btnTestMessage" onserverclick="btnTestMessage_Click">
                    📤 Tester Envoi Message
                </button>
                <div id="resultMessage" runat="server" visible="false" class="result">
                    <asp:Label ID="lblResultMessage" runat="server"></asp:Label>
                </div>
            </div>

            <!-- Section 2: Test Pièce Jointe -->
            <div class="section">
                <h3>2. Test Pièce Jointe</h3>
                <div class="form-group">
                    <label>Fichier :</label>
                    <input type="file" id="fileUpload" runat="server" />
                </div>
                <button type="button" runat="server" id="btnTestFile" onserverclick="btnTestFile_Click">
                    📎 Tester Upload
                </button>
                <div id="resultFile" runat="server" visible="false" class="result">
                    <asp:Label ID="lblResultFile" runat="server"></asp:Label>
                </div>
            </div>

            <!-- Section 3: Test Emojis -->
            <div class="section">
                <h3>3. Test Conversion Emojis</h3>
                <div class="form-group">
                    <label>Texte avec emojis :</label>
                    <input type="text" id="txtEmojis" runat="server" value="Salut :smile: ça va? :heart: :thumbs_up:" />
                </div>
                <button type="button" runat="server" id="btnTestEmojis" onserverclick="btnTestEmojis_Click">
                    😀 Tester Emojis
                </button>
                <div id="resultEmojis" runat="server" visible="false" class="result">
                    <asp:Label ID="lblResultEmojis" runat="server"></asp:Label>
                </div>
            </div>

            <!-- Section 4: Test Base de Données -->
            <div class="section">
                <h3>4. Test Connexion Base de Données</h3>
                <button type="button" runat="server" id="btnTestDB" onserverclick="btnTestDB_Click">
                    🗄️ Tester DB
                </button>
                <div id="resultDB" runat="server" visible="false" class="result">
                    <asp:Label ID="lblResultDB" runat="server"></asp:Label>
                </div>
            </div>

            <!-- Section 5: Informations Système -->
            <div class="section">
                <h3>5. Informations Système</h3>
                <div class="info">
                    <strong>Session ID Membre :</strong> <asp:Label ID="lblSessionId" runat="server"></asp:Label><br/>
                    <strong>Heure Serveur :</strong> <asp:Label ID="lblHeureServeur" runat="server"></asp:Label><br/>
                    <strong>Nombre de fichiers dans Request :</strong> <asp:Label ID="lblNbFichiers" runat="server"></asp:Label><br/>
                    <strong>User Agent :</strong> <asp:Label ID="lblUserAgent" runat="server"></asp:Label>
                </div>
            </div>

            <!-- Section 6: Logs Debug -->
            <div class="section">
                <h3>6. Logs Debug</h3>
                <div class="debug-log">
                    <asp:Label ID="lblDebugLogs" runat="server" Text="Aucun log pour le moment..."></asp:Label>
                </div>
                <button type="button" runat="server" id="btnClearLogs" onserverclick="btnClearLogs_Click">
                    🗑️ Effacer Logs
                </button>
            </div>

            <!-- Champs cachés pour simulation -->
            <input type="hidden" id="hdnAttachmentPath" runat="server" />
            <input type="hidden" id="lblId" runat="server" />
        </div>
    </form>

    <script>
        // Synchroniser les champs
        document.addEventListener('DOMContentLoaded', function() {
            const txtDestinataire = document.getElementById('<%= txtDestinataire.ClientID %>');
            const lblId = document.getElementById('<%= lblId.ClientID %>');
            
            if (txtDestinataire && lblId) {
                txtDestinataire.addEventListener('change', function() {
                    lblId.value = this.value;
                });
                lblId.value = txtDestinataire.value;
            }
        });
    </script>
</body>
</html>
