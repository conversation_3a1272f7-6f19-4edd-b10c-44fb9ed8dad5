// Messagerie LinkedIn Style - JavaScript

class MessagerieLinkedIn {
    constructor() {
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.setupEmojiPicker();
        this.setupFileUpload();
        this.setupAutoRefresh();
        this.scrollToBottom();
        this.updateCharCount();
    }

    setupEventListeners() {
        // Gestion du textarea
        const textarea = document.getElementById('ContentPlaceHolder1_txtMessage');
        if (textarea) {
            textarea.addEventListener('input', () => this.updateCharCount());
            textarea.addEventListener('keypress', (e) => this.handleKeyPress(e));
            textarea.addEventListener('paste', () => {
                setTimeout(() => this.updateCharCount(), 10);
            });
        }

        // Gestion des boutons
        const btnEmoji = document.getElementById('btnEmoji');
        const btnAttachment = document.getElementById('btnAttachment');
        const btnRemoveAttachment = document.getElementById('btnRemoveAttachment');

        if (btnEmoji) {
            btnEmoji.addEventListener('click', () => this.toggleEmojiPicker());
        }

        if (btnAttachment) {
            btnAttachment.addEventListener('click', () => this.openFileDialog());
        }

        if (btnRemoveAttachment) {
            btnRemoveAttachment.addEventListener('click', () => this.removeAttachment());
        }

        // Fermer le sélecteur d'émojis en cliquant ailleurs
        document.addEventListener('click', (e) => this.handleOutsideClick(e));

        // Gestion des contacts actifs
        this.setupContactSelection();
    }

    setupContactSelection() {
        const contacts = document.querySelectorAll('.contact-item');
        contacts.forEach(contact => {
            contact.addEventListener('click', (e) => {
                // Retirer la classe active de tous les contacts
                contacts.forEach(c => c.classList.remove('active'));
                // Ajouter la classe active au contact cliqué
                contact.classList.add('active');
            });
        });
    }

    handleKeyPress(event) {
        if (event.keyCode === 13 && !event.shiftKey) {
            event.preventDefault();
            const btnEnvoie = document.getElementById('ContentPlaceHolder1_btnenvoie');
            if (btnEnvoie && this.isMessageValid()) {
                btnEnvoie.click();
            }
        }
    }

    isMessageValid() {
        const textarea = document.getElementById('ContentPlaceHolder1_txtMessage');
        return textarea && textarea.value.trim().length > 0;
    }

    updateCharCount() {
        const textarea = document.getElementById('ContentPlaceHolder1_txtMessage');
        const counter = document.getElementById('charCount');
        
        if (textarea && counter) {
            const length = textarea.value.length;
            counter.textContent = `${length}/1000 caractères`;

            // Changer la couleur selon la longueur
            if (length > 900) {
                counter.style.color = '#e74c3c';
            } else if (length > 800) {
                counter.style.color = '#f39c12';
            } else {
                counter.style.color = '#666';
            }

            // Activer/désactiver le bouton d'envoi
            const sendBtn = document.getElementById('ContentPlaceHolder1_btnenvoie');
            if (sendBtn) {
                sendBtn.disabled = length === 0;
            }
        }
    }

    scrollToBottom() {
        const chatBody = document.querySelector('.chat-body');
        if (chatBody) {
            chatBody.scrollTop = chatBody.scrollHeight;
        }
    }

    // Gestion des émojis
    setupEmojiPicker() {
        const emojiItems = document.querySelectorAll('.emoji-item');
        emojiItems.forEach(item => {
            item.addEventListener('click', () => {
                this.insertEmoji(item.dataset.emoji);
            });
        });

        // Gestion des onglets d'émojis
        const emojiTabs = document.querySelectorAll('.emoji-tab');
        emojiTabs.forEach(tab => {
            tab.addEventListener('click', () => {
                this.switchEmojiCategory(tab.dataset.category);
            });
        });
    }

    toggleEmojiPicker() {
        const picker = document.getElementById('emojiPicker');
        if (picker) {
            const isVisible = picker.style.display !== 'none';
            picker.style.display = isVisible ? 'none' : 'block';
        }
    }

    insertEmoji(emoji) {
        const textarea = document.getElementById('ContentPlaceHolder1_txtMessage');
        if (textarea) {
            const cursorPos = textarea.selectionStart;
            const textBefore = textarea.value.substring(0, cursorPos);
            const textAfter = textarea.value.substring(cursorPos);

            textarea.value = textBefore + emoji + textAfter;
            textarea.focus();
            textarea.setSelectionRange(cursorPos + emoji.length, cursorPos + emoji.length);

            this.updateCharCount();
            this.toggleEmojiPicker();
        }
    }

    switchEmojiCategory(category) {
        // Retirer la classe active de tous les onglets
        document.querySelectorAll('.emoji-tab').forEach(tab => {
            tab.classList.remove('active');
        });

        // Ajouter la classe active à l'onglet cliqué
        document.querySelector(`[data-category="${category}"]`).classList.add('active');

        // Charger les émojis de la catégorie
        this.loadEmojiCategory(category);
    }

    loadEmojiCategory(category) {
        const emojiContent = document.getElementById('emojiContent');
        if (!emojiContent) return;

        const emojiCategories = {
            populaires: ['😊', '😂', '❤️', '👍', '😢', '😉', '🔥', '🎉', '😍', '👏'],
            visages: ['😀', '😃', '😄', '😁', '😆', '😅', '😂', '🤣', '😊', '😇'],
            gestes: ['👍', '👎', '👌', '✌️', '🤞', '🤟', '🤘', '🤙', '👈', '👉'],
            objets: ['❤️', '💙', '💚', '💛', '🧡', '💜', '🖤', '🤍', '🤎', '💔'],
            nature: ['🌳', '🌲', '🌱', '🌿', '🍀', '🌸', '🌺', '🌻', '🌷', '🌹']
        };

        const emojis = emojiCategories[category] || emojiCategories.populaires;
        
        emojiContent.innerHTML = `
            <div class="emoji-grid">
                ${emojis.map(emoji => 
                    `<span class="emoji-item" data-emoji="${emoji}">${emoji}</span>`
                ).join('')}
            </div>
        `;

        // Réattacher les événements
        emojiContent.querySelectorAll('.emoji-item').forEach(item => {
            item.addEventListener('click', () => {
                this.insertEmoji(item.dataset.emoji);
            });
        });
    }

    handleOutsideClick(e) {
        const picker = document.getElementById('emojiPicker');
        const btnEmoji = document.getElementById('btnEmoji');

        if (picker && btnEmoji && !picker.contains(e.target) && e.target !== btnEmoji) {
            picker.style.display = 'none';
        }
    }

    // Gestion des fichiers
    setupFileUpload() {
        const fileInput = document.getElementById('fileAttachment');
        if (fileInput) {
            fileInput.addEventListener('change', () => this.handleFileSelect());
        }
    }

    openFileDialog() {
        const fileInput = document.getElementById('fileAttachment');
        if (fileInput) {
            fileInput.click();
        }
    }

    handleFileSelect() {
        const fileInput = document.getElementById('fileAttachment');
        const file = fileInput.files[0];

        if (file) {
            // Validation de la taille (10MB max)
            if (file.size > 10 * 1024 * 1024) {
                this.showAlert('Le fichier est trop volumineux (maximum 10 Mo)');
                fileInput.value = '';
                return;
            }

            // Validation de l'extension
            const allowedExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.pdf', '.doc', '.docx', '.txt', '.zip', '.rar', '.mp3', '.mp4', '.avi'];
            const fileExtension = file.name.toLowerCase().substring(file.name.lastIndexOf('.'));

            if (allowedExtensions.indexOf(fileExtension) === -1) {
                this.showAlert('Type de fichier non autorisé. Extensions autorisées: ' + allowedExtensions.join(', '));
                fileInput.value = '';
                return;
            }

            // Afficher la prévisualisation
            this.showAttachmentPreview(file);
        }
    }

    showAttachmentPreview(file) {
        const preview = document.getElementById('attachmentPreview');
        const nameSpan = document.getElementById('attachmentName');
        const sizeSpan = document.getElementById('attachmentSize');

        if (preview && nameSpan && sizeSpan) {
            nameSpan.textContent = file.name;
            sizeSpan.textContent = this.formatFileSize(file.size);
            preview.style.display = 'block';

            // Marquer qu'un fichier est prêt à être uploadé
            const hiddenField = document.getElementById('ContentPlaceHolder1_hdnAttachmentPath');
            if (hiddenField) {
                hiddenField.value = 'READY_TO_UPLOAD';
            }
        }
    }

    removeAttachment() {
        const fileInput = document.getElementById('fileAttachment');
        const preview = document.getElementById('attachmentPreview');
        const hiddenField = document.getElementById('ContentPlaceHolder1_hdnAttachmentPath');

        if (fileInput) fileInput.value = '';
        if (preview) preview.style.display = 'none';
        if (hiddenField) hiddenField.value = '';
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
    }

    // Auto-refresh des messages
    setupAutoRefresh() {
        setInterval(() => {
            const lblId = document.getElementById('ContentPlaceHolder1_lblId');
            if (lblId && lblId.textContent !== '0') {
                // Déclencher un postback pour actualiser les messages
                __doPostBack('ContentPlaceHolder1', 'RefreshMessages');
            }
        }, 30000); // 30 secondes
    }

    showAlert(message) {
        // Utiliser une notification moderne au lieu d'alert
        if (typeof toastr !== 'undefined') {
            toastr.error(message);
        } else {
            alert(message);
        }
    }

    // Méthode pour ajouter un indicateur de frappe
    showTypingIndicator(show = true) {
        const indicator = document.querySelector('.typing-indicator');
        if (indicator) {
            indicator.classList.toggle('active', show);
        }
    }
}

// Initialiser la messagerie quand le DOM est chargé
document.addEventListener('DOMContentLoaded', () => {
    window.messagerieLinkedIn = new MessagerieLinkedIn();
});

// Fonction globale pour le scroll après postback
function scrollToBottomAfterPostback() {
    if (window.messagerieLinkedIn) {
        setTimeout(() => {
            window.messagerieLinkedIn.scrollToBottom();
        }, 100);
    }
}
