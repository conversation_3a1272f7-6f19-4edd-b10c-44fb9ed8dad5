// Messagerie LinkedIn Style - JavaScript

class MessagerieLinkedIn {
    constructor() {
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.setupEmojiPicker();
        this.setupFileUpload();
        this.setupAutoRefresh();
        this.scrollToBottom();
        this.updateCharCount();
    }

    setupEventListeners() {
        // Gestion du textarea LinkedIn-style
        const textarea = document.getElementById('ContentPlaceHolder1_txtMessage');
        const placeholder = document.getElementById('inputPlaceholder');

        if (textarea) {
            // Événements de base
            textarea.addEventListener('input', (e) => {
                this.updateCharCount();
                this.handleTextareaInput(e);
                this.checkMentions(e);
                this.autoResize(textarea);
            });

            textarea.addEventListener('keydown', (e) => this.handleKeyDown(e));
            textarea.addEventListener('keypress', (e) => this.handleKeyPress(e));
            textarea.addEventListener('focus', () => this.handleFocus());
            textarea.addEventListener('blur', () => this.handleBlur());
            textarea.addEventListener('paste', () => {
                setTimeout(() => {
                    this.updateCharCount();
                    this.autoResize(textarea);
                }, 10);
            });

            // Gestion du placeholder
            if (placeholder) {
                this.updatePlaceholder();
            }
        }

        // Gestion des boutons de formatage
        this.setupFormattingButtons();

        // Gestion des boutons d'action
        const btnEmoji = document.getElementById('btnEmoji');
        const btnAttachment = document.getElementById('btnAttachment');
        const btnGif = document.getElementById('btnGif');
        const btnSchedule = document.getElementById('btnSchedule');

        if (btnEmoji) {
            btnEmoji.addEventListener('click', () => this.toggleEmojiPicker());
        }

        if (btnAttachment) {
            btnAttachment.addEventListener('click', () => this.openFileDialog());
        }

        if (btnGif) {
            btnGif.addEventListener('click', () => this.openGifPicker());
        }

        if (btnSchedule) {
            btnSchedule.addEventListener('click', () => this.openScheduleDialog());
        }

        // Fermer les popups en cliquant ailleurs
        document.addEventListener('click', (e) => this.handleOutsideClick(e));

        // Gestion des contacts actifs
        this.setupContactSelection();

        // Gestion des suggestions
        this.setupSuggestions();
    }

    setupContactSelection() {
        const contacts = document.querySelectorAll('.contact-item');
        contacts.forEach(contact => {
            contact.addEventListener('click', (e) => {
                // Retirer la classe active de tous les contacts
                contacts.forEach(c => c.classList.remove('active'));
                // Ajouter la classe active au contact cliqué
                contact.classList.add('active');
            });
        });
    }

    handleKeyPress(event) {
        if (event.keyCode === 13 && !event.shiftKey) {
            event.preventDefault();
            const btnEnvoie = document.getElementById('ContentPlaceHolder1_btnenvoie');
            if (btnEnvoie && this.isMessageValid()) {
                btnEnvoie.click();
            }
        }
    }

    isMessageValid() {
        const textarea = document.getElementById('ContentPlaceHolder1_txtMessage');
        return textarea && textarea.value.trim().length > 0;
    }

    updateCharCount() {
        const textarea = document.getElementById('ContentPlaceHolder1_txtMessage');
        const counter = document.getElementById('charCounter');

        if (textarea && counter) {
            const length = textarea.value.length;
            counter.textContent = `${length}/1000`;

            // Changer la couleur selon la longueur
            counter.classList.remove('warning', 'danger');
            if (length > 900) {
                counter.classList.add('danger');
            } else if (length > 800) {
                counter.classList.add('warning');
            }

            // Activer/désactiver le bouton d'envoi
            const sendBtn = document.getElementById('ContentPlaceHolder1_btnenvoie');
            if (sendBtn) {
                sendBtn.disabled = length === 0 || length > 1000;
            }
        }
    }

    // Nouvelles méthodes pour les fonctionnalités LinkedIn-style
    handleTextareaInput(event) {
        const textarea = event.target;
        this.updatePlaceholder();
        this.showTypingIndicator();

        // Débounce pour arrêter l'indicateur de frappe
        clearTimeout(this.typingTimeout);
        this.typingTimeout = setTimeout(() => {
            this.hideTypingIndicator();
        }, 2000);
    }

    updatePlaceholder() {
        const textarea = document.getElementById('ContentPlaceHolder1_txtMessage');
        const placeholder = document.getElementById('inputPlaceholder');

        if (textarea && placeholder) {
            if (textarea.value.length > 0 || textarea === document.activeElement) {
                placeholder.classList.add('hidden');
            } else {
                placeholder.classList.remove('hidden');
            }
        }
    }

    handleFocus() {
        this.updatePlaceholder();
        const composer = document.querySelector('.linkedin-message-composer');
        if (composer) {
            composer.classList.add('focused');
        }
    }

    handleBlur() {
        this.updatePlaceholder();
        const composer = document.querySelector('.linkedin-message-composer');
        if (composer) {
            composer.classList.remove('focused');
        }
    }

    autoResize(textarea) {
        textarea.style.height = 'auto';
        const newHeight = Math.min(textarea.scrollHeight, 120);
        textarea.style.height = newHeight + 'px';
    }

    handleKeyDown(event) {
        const textarea = event.target;

        // Gestion des raccourcis de formatage
        if (event.ctrlKey || event.metaKey) {
            switch (event.key.toLowerCase()) {
                case 'b':
                    event.preventDefault();
                    this.toggleFormatting('bold');
                    break;
                case 'i':
                    event.preventDefault();
                    this.toggleFormatting('italic');
                    break;
                case 'u':
                    event.preventDefault();
                    this.toggleFormatting('underline');
                    break;
                case 'e':
                    event.preventDefault();
                    this.toggleEmojiPicker();
                    break;
            }
        }

        // Navigation dans les suggestions
        if (this.suggestionsVisible) {
            switch (event.key) {
                case 'ArrowUp':
                    event.preventDefault();
                    this.navigateSuggestions(-1);
                    break;
                case 'ArrowDown':
                    event.preventDefault();
                    this.navigateSuggestions(1);
                    break;
                case 'Enter':
                    if (this.selectedSuggestion >= 0) {
                        event.preventDefault();
                        this.selectSuggestion(this.selectedSuggestion);
                    }
                    break;
                case 'Escape':
                    this.hideSuggestions();
                    break;
            }
        }
    }

    setupFormattingButtons() {
        const formatButtons = {
            'btnBold': 'bold',
            'btnItalic': 'italic',
            'btnUnderline': 'underline'
        };

        Object.entries(formatButtons).forEach(([btnId, format]) => {
            const btn = document.getElementById(btnId);
            if (btn) {
                btn.addEventListener('click', () => this.toggleFormatting(format));
            }
        });
    }

    toggleFormatting(format) {
        const textarea = document.getElementById('ContentPlaceHolder1_txtMessage');
        if (!textarea) return;

        const start = textarea.selectionStart;
        const end = textarea.selectionEnd;
        const selectedText = textarea.value.substring(start, end);

        if (selectedText) {
            let formattedText;
            switch (format) {
                case 'bold':
                    formattedText = `**${selectedText}**`;
                    break;
                case 'italic':
                    formattedText = `*${selectedText}*`;
                    break;
                case 'underline':
                    formattedText = `__${selectedText}__`;
                    break;
                default:
                    return;
            }

            const newValue = textarea.value.substring(0, start) + formattedText + textarea.value.substring(end);
            textarea.value = newValue;
            textarea.focus();
            textarea.setSelectionRange(start + formattedText.length, start + formattedText.length);

            this.updateCharCount();
            this.autoResize(textarea);
        }

        // Toggle button state
        const btn = document.getElementById(`btn${format.charAt(0).toUpperCase() + format.slice(1)}`);
        if (btn) {
            btn.classList.toggle('active');
            setTimeout(() => btn.classList.remove('active'), 200);
        }
    }

    scrollToBottom() {
        const chatBody = document.querySelector('.chat-body');
        if (chatBody) {
            chatBody.scrollTop = chatBody.scrollHeight;
        }
    }

    // Gestion des mentions et suggestions
    checkMentions(event) {
        const textarea = event.target;
        const cursorPos = textarea.selectionStart;
        const textBeforeCursor = textarea.value.substring(0, cursorPos);
        const lastAtIndex = textBeforeCursor.lastIndexOf('@');

        if (lastAtIndex !== -1) {
            const textAfterAt = textBeforeCursor.substring(lastAtIndex + 1);
            if (textAfterAt.length >= 0 && !textAfterAt.includes(' ')) {
                this.showSuggestions(textAfterAt, lastAtIndex);
            } else {
                this.hideSuggestions();
            }
        } else {
            this.hideSuggestions();
        }
    }

    setupSuggestions() {
        this.suggestionsVisible = false;
        this.selectedSuggestion = -1;
        this.suggestions = [];

        // Données de test pour les suggestions (à remplacer par des données réelles)
        this.contactSuggestions = [
            { id: 1, name: 'Jean Dupont', title: 'Développeur', avatar: '../file/membr/default.jpg' },
            { id: 2, name: 'Marie Martin', title: 'Designer', avatar: '../file/membr/default.jpg' },
            { id: 3, name: 'Pierre Durand', title: 'Chef de projet', avatar: '../file/membr/default.jpg' }
        ];
    }

    showSuggestions(query, atIndex) {
        const container = document.getElementById('suggestionsContainer');
        const list = document.getElementById('suggestionsList');

        if (!container || !list) return;

        // Filtrer les suggestions
        this.suggestions = this.contactSuggestions.filter(contact =>
            contact.name.toLowerCase().includes(query.toLowerCase())
        );

        if (this.suggestions.length === 0) {
            this.hideSuggestions();
            return;
        }

        // Générer le HTML des suggestions
        list.innerHTML = this.suggestions.map((contact, index) => `
            <div class="suggestion-item" data-index="${index}">
                <img src="${contact.avatar}" alt="${contact.name}" class="suggestion-avatar">
                <div class="suggestion-info">
                    <div class="suggestion-name">${contact.name}</div>
                    <div class="suggestion-title">${contact.title}</div>
                </div>
            </div>
        `).join('');

        // Ajouter les événements
        list.querySelectorAll('.suggestion-item').forEach((item, index) => {
            item.addEventListener('click', () => this.selectSuggestion(index));
            item.addEventListener('mouseenter', () => this.highlightSuggestion(index));
        });

        container.style.display = 'block';
        this.suggestionsVisible = true;
        this.selectedSuggestion = 0;
        this.highlightSuggestion(0);
    }

    hideSuggestions() {
        const container = document.getElementById('suggestionsContainer');
        if (container) {
            container.style.display = 'none';
        }
        this.suggestionsVisible = false;
        this.selectedSuggestion = -1;
    }

    navigateSuggestions(direction) {
        if (!this.suggestionsVisible || this.suggestions.length === 0) return;

        this.selectedSuggestion += direction;

        if (this.selectedSuggestion < 0) {
            this.selectedSuggestion = this.suggestions.length - 1;
        } else if (this.selectedSuggestion >= this.suggestions.length) {
            this.selectedSuggestion = 0;
        }

        this.highlightSuggestion(this.selectedSuggestion);
    }

    highlightSuggestion(index) {
        const items = document.querySelectorAll('.suggestion-item');
        items.forEach((item, i) => {
            item.classList.toggle('selected', i === index);
        });
    }

    selectSuggestion(index) {
        if (index < 0 || index >= this.suggestions.length) return;

        const textarea = document.getElementById('ContentPlaceHolder1_txtMessage');
        if (!textarea) return;

        const suggestion = this.suggestions[index];
        const cursorPos = textarea.selectionStart;
        const textBeforeCursor = textarea.value.substring(0, cursorPos);
        const lastAtIndex = textBeforeCursor.lastIndexOf('@');

        if (lastAtIndex !== -1) {
            const textBefore = textarea.value.substring(0, lastAtIndex);
            const textAfter = textarea.value.substring(cursorPos);
            const mention = `@${suggestion.name} `;

            textarea.value = textBefore + mention + textAfter;
            const newCursorPos = lastAtIndex + mention.length;
            textarea.focus();
            textarea.setSelectionRange(newCursorPos, newCursorPos);

            this.updateCharCount();
            this.autoResize(textarea);
        }

        this.hideSuggestions();
    }

    // Indicateur de frappe
    showTypingIndicator() {
        const indicator = document.getElementById('typingIndicator');
        if (indicator) {
            indicator.style.display = 'flex';
        }
    }

    hideTypingIndicator() {
        const indicator = document.getElementById('typingIndicator');
        if (indicator) {
            indicator.style.display = 'none';
        }
    }

    // Nouvelles fonctionnalités
    openGifPicker() {
        // Placeholder pour le sélecteur de GIF
        this.showAlert('Fonctionnalité GIF à venir !');
    }

    openScheduleDialog() {
        // Placeholder pour la programmation d'envoi
        this.showAlert('Fonctionnalité de programmation à venir !');
    }

    // Gestion des émojis
    setupEmojiPicker() {
        const emojiItems = document.querySelectorAll('.emoji-item');
        emojiItems.forEach(item => {
            item.addEventListener('click', () => {
                this.insertEmoji(item.dataset.emoji);
            });
        });

        // Gestion des onglets d'émojis
        const emojiTabs = document.querySelectorAll('.emoji-tab');
        emojiTabs.forEach(tab => {
            tab.addEventListener('click', () => {
                this.switchEmojiCategory(tab.dataset.category);
            });
        });
    }

    toggleEmojiPicker() {
        const picker = document.getElementById('emojiPicker');
        if (picker) {
            const isVisible = picker.style.display !== 'none';
            picker.style.display = isVisible ? 'none' : 'block';
        }
    }

    insertEmoji(emoji) {
        const textarea = document.getElementById('ContentPlaceHolder1_txtMessage');
        if (textarea) {
            const cursorPos = textarea.selectionStart;
            const textBefore = textarea.value.substring(0, cursorPos);
            const textAfter = textarea.value.substring(cursorPos);

            textarea.value = textBefore + emoji + textAfter;
            textarea.focus();
            textarea.setSelectionRange(cursorPos + emoji.length, cursorPos + emoji.length);

            this.updateCharCount();
            this.toggleEmojiPicker();
        }
    }

    switchEmojiCategory(category) {
        // Retirer la classe active de tous les onglets
        document.querySelectorAll('.emoji-tab').forEach(tab => {
            tab.classList.remove('active');
        });

        // Ajouter la classe active à l'onglet cliqué
        document.querySelector(`[data-category="${category}"]`).classList.add('active');

        // Charger les émojis de la catégorie
        this.loadEmojiCategory(category);
    }

    loadEmojiCategory(category) {
        const emojiContent = document.getElementById('emojiContent');
        if (!emojiContent) return;

        const emojiCategories = {
            populaires: ['😊', '😂', '❤️', '👍', '😢', '😉', '🔥', '🎉', '😍', '👏'],
            visages: ['😀', '😃', '😄', '😁', '😆', '😅', '😂', '🤣', '😊', '😇'],
            gestes: ['👍', '👎', '👌', '✌️', '🤞', '🤟', '🤘', '🤙', '👈', '👉'],
            objets: ['❤️', '💙', '💚', '💛', '🧡', '💜', '🖤', '🤍', '🤎', '💔'],
            nature: ['🌳', '🌲', '🌱', '🌿', '🍀', '🌸', '🌺', '🌻', '🌷', '🌹']
        };

        const emojis = emojiCategories[category] || emojiCategories.populaires;
        
        emojiContent.innerHTML = `
            <div class="emoji-grid">
                ${emojis.map(emoji => 
                    `<span class="emoji-item" data-emoji="${emoji}">${emoji}</span>`
                ).join('')}
            </div>
        `;

        // Réattacher les événements
        emojiContent.querySelectorAll('.emoji-item').forEach(item => {
            item.addEventListener('click', () => {
                this.insertEmoji(item.dataset.emoji);
            });
        });
    }

    handleOutsideClick(e) {
        const picker = document.getElementById('emojiPicker');
        const btnEmoji = document.getElementById('btnEmoji');

        if (picker && btnEmoji && !picker.contains(e.target) && e.target !== btnEmoji) {
            picker.style.display = 'none';
        }
    }

    // Gestion des fichiers
    setupFileUpload() {
        const fileInput = document.getElementById('fileAttachment');
        if (fileInput) {
            fileInput.addEventListener('change', () => this.handleFileSelect());
        }
    }

    openFileDialog() {
        const fileInput = document.getElementById('fileAttachment');
        if (fileInput) {
            fileInput.click();
        }
    }

    handleFileSelect() {
        const fileInput = document.getElementById('fileAttachment');
        const file = fileInput.files[0];

        if (file) {
            // Validation de la taille (10MB max)
            if (file.size > 10 * 1024 * 1024) {
                this.showAlert('Le fichier est trop volumineux (maximum 10 Mo)');
                fileInput.value = '';
                return;
            }

            // Validation de l'extension
            const allowedExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.pdf', '.doc', '.docx', '.txt', '.zip', '.rar', '.mp3', '.mp4', '.avi'];
            const fileExtension = file.name.toLowerCase().substring(file.name.lastIndexOf('.'));

            if (allowedExtensions.indexOf(fileExtension) === -1) {
                this.showAlert('Type de fichier non autorisé. Extensions autorisées: ' + allowedExtensions.join(', '));
                fileInput.value = '';
                return;
            }

            // Afficher la prévisualisation
            this.showAttachmentPreview(file);
        }
    }

    showAttachmentPreview(file) {
        const preview = document.getElementById('attachmentPreview');
        const nameSpan = document.getElementById('attachmentName');
        const sizeSpan = document.getElementById('attachmentSize');

        if (preview && nameSpan && sizeSpan) {
            nameSpan.textContent = file.name;
            sizeSpan.textContent = this.formatFileSize(file.size);
            preview.style.display = 'block';

            // Marquer qu'un fichier est prêt à être uploadé
            const hiddenField = document.getElementById('ContentPlaceHolder1_hdnAttachmentPath');
            if (hiddenField) {
                hiddenField.value = 'READY_TO_UPLOAD';
            }
        }
    }

    removeAttachment() {
        const fileInput = document.getElementById('fileAttachment');
        const preview = document.getElementById('attachmentPreview');
        const hiddenField = document.getElementById('ContentPlaceHolder1_hdnAttachmentPath');

        if (fileInput) fileInput.value = '';
        if (preview) preview.style.display = 'none';
        if (hiddenField) hiddenField.value = '';
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
    }

    // Auto-refresh des messages
    setupAutoRefresh() {
        setInterval(() => {
            const lblId = document.getElementById('ContentPlaceHolder1_lblId');
            if (lblId && lblId.textContent !== '0') {
                // Déclencher un postback pour actualiser les messages
                __doPostBack('ContentPlaceHolder1', 'RefreshMessages');
            }
        }, 30000); // 30 secondes
    }

    showAlert(message) {
        // Utiliser une notification moderne au lieu d'alert
        if (typeof toastr !== 'undefined') {
            toastr.error(message);
        } else {
            alert(message);
        }
    }

    // Méthode pour ajouter un indicateur de frappe
    showTypingIndicator(show = true) {
        const indicator = document.querySelector('.typing-indicator');
        if (indicator) {
            indicator.classList.toggle('active', show);
        }
    }
}

// Initialiser la messagerie quand le DOM est chargé
document.addEventListener('DOMContentLoaded', () => {
    window.messagerieLinkedIn = new MessagerieLinkedIn();
});

// Fonction globale pour le scroll après postback
function scrollToBottomAfterPostback() {
    if (window.messagerieLinkedIn) {
        setTimeout(() => {
            window.messagerieLinkedIn.scrollToBottom();
        }, 100);
    }
}
