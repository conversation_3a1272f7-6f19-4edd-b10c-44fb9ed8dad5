<%@ Page Title="" Language="C#" MasterPageFile="~/PageMaster.Master" AutoEventWireup="true" CodeBehind="messagerie.aspx.cs" Inherits="LinCom.messagerie" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
    <link href="css/messagerie-linkedin-style.css" rel="stylesheet" type="text/css" />
    <script src="js/messagerie-linkedin.js" type="text/javascript"></script>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <!-- Page Title -->
    <div class="page-title">
        <div class="heading">
            <div class="container">
                <div class="row d-flex justify-content-center text-center">
                    <div class="col-lg-8">
                    </div>
                </div>
            </div>
        </div>
        <nav class="breadcrumbs">
            <div class="container">
                <ol>
                    <li><a href="home.aspx">Home</a></li>
                    <li><a href="#">Espace FNUAP</a></li>
                    <li><a href="#">Bibliothèque Digitale de FNUAP</a></li>
                    <li class="current"><a href="ressources-document.aspx">Documents officiels</a></li>
                </ol>
            </div>
        </nav>
    </div>
    <!-- End Page Title -->
    <main class="main">
        <asp:HiddenField ID="hdnConversationId" runat="server" />
<asp:HiddenField ID="hdnIsGroup" runat="server" />
<asp:HiddenField ID="hdnCurrentUserId" runat="server" />

        <div class="container py-4">
            <h2 class="mb-4">💬 Espace Messagerie</h2>

            <div class="chat-wrapper">

                <!-- Sidebar -->
                <div class="contacts-panel">
                    <!-- En-tête Messages style LinkedIn -->
                    <div class="linkedin-messages-header">
                        <div class="messages-title">
                            <div class="title-main">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor" class="messages-icon">
                                    <path d="M20 2H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h4l4 4 4-4h4c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-2 12H6v-2h12v2zm0-3H6V9h12v2zm0-3H6V6h12v2z"/>
                                </svg>
                                <span class="title-text">Messages</span>
                            </div>
                            <div class="messages-actions">
                                <button type="button" class="header-action-btn" title="Nouvelle conversation">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"/>
                                    </svg>
                                </button>
                                <button type="button" class="header-action-btn" title="Plus d'options">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M12 8c1.1 0 2-.9 2-2s-.9-2-2-2-2 .9-2 2 .9 2 2 2zm0 2c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm0 6c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z"/>
                                    </svg>
                                </button>
                            </div>
                        </div>

                        <!-- Onglets Messages/Conversations -->
                        <div class="messages-tabs">
                            <button type="button" class="tab-btn active" data-tab="messages">
                                <span>Messages</span>
                                <span class="tab-count">12</span>
                            </button>
                            <button type="button" class="tab-btn" data-tab="conversations">
                                <span>Conversations</span>
                            </button>
                        </div>
                    </div>

                    <!-- Barre de recherche LinkedIn style -->
                    <div class="linkedin-search-container">
                        <div class="search-input-wrapper">
                            <div class="search-icon">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
                                </svg>
                            </div>
                            <asp:TextBox ID="txtRechercheContact" runat="server"
                                         placeholder="Rechercher dans les messages..."
                                         AutoPostBack="true"
                                         OnTextChanged="txtRechercheContact_TextChanged"
                                         CssClass="linkedin-search-input"></asp:TextBox>
                            <div class="search-actions">
                                <button type="button" class="search-filter-btn" title="Filtres de recherche">
                                    <svg width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M10 18h4v-2h-4v2zM3 6v2h18V6H3zm3 7h12v-2H6v2z"/>
                                    </svg>
                                </button>
                                <button type="button" class="search-clear-btn" id="clearSearchBtn" style="display: none;" title="Effacer la recherche">
                                    <svg width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
                                    </svg>
                                </button>
                            </div>
                        </div>

                        <!-- Suggestions de recherche -->
                        <div class="search-suggestions" id="searchSuggestions" style="display: none;">
                            <div class="suggestions-header">
                                <span>Recherches récentes</span>
                                <button type="button" class="clear-history-btn">Effacer</button>
                            </div>
                            <div class="suggestions-list">
                                <div class="suggestion-item">
                                    <svg width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M13 3c-4.97 0-9 4.03-9 9H1l3.89 3.89.07.14L9 12H6c0-3.87 3.13-7 7-7s7 3.13 7 7-3.13 7-7 7c-1.93 0-3.68-.79-4.94-2.06l-1.42 1.42C8.27 19.99 10.51 21 13 21c4.97 0 9-4.03 9-9s-4.03-9-9-9zm-1 5v5l4.28 2.54.72-1.21-3.5-2.08V8H12z"/>
                                    </svg>
                                    <span>projet collaboration</span>
                                </div>
                                <div class="suggestion-item">
                                    <svg width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M13 3c-4.97 0-9 4.03-9 9H1l3.89 3.89.07.14L9 12H6c0-3.87 3.13-7 7-7s7 3.13 7 7-3.13 7-7 7c-1.93 0-3.68-.79-4.94-2.06l-1.42 1.42C8.27 19.99 10.51 21 13 21c4.97 0 9-4.03 9-9s-4.03-9-9-9zm-1 5v5l4.28 2.54.72-1.21-3.5-2.08V8H12z"/>
                                    </svg>
                                    <span>réunion équipe</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Liste des contacts scrollable -->
                    <div class="contacts-list">
                        <asp:ListView ID="listmembre" runat="server" OnItemCommand="listmembre_ItemCommand">
                        <EmptyDataTemplate>
                            <div style="padding: 20px; text-align: center; color: #666;">
                                <p>Aucun contact trouvé</p>
                                <small>Commencez une nouvelle conversation</small>
                            </div>
                        </EmptyDataTemplate>
                        <ItemTemplate>
                            <asp:LinkButton ID="btnSelectMembre" runat="server"
                                CommandName="viewmem"
                                CommandArgument='<%# Eval("id") %>'
                                CssClass="linkedin-contact-item">

                                <!-- Photo de profil avec indicateur en ligne -->
                                <div class="contact-avatar-container">
                                    <img src='<%# HttpUtility.HtmlEncode(string.Concat("../file/membr/", Eval("PhotoProfil"))) %>'
                                         alt="Photo de profil"
                                         class="contact-avatar" />
                                    <div class="online-status-indicator <%# IsContactOnline(Eval("id")) ? "" : "offline" %>"></div>
                                </div>

                                <!-- Informations du contact -->
                                <div class="contact-main-info">
                                    <div class="contact-header">
                                        <div class="contact-name"><%# HttpUtility.HtmlEncode(Eval("Membre")) %></div>
                                        <div class="message-time">
                                            <%# GetLastMessageTime(Eval("id")) %>
                                        </div>
                                    </div>
                                    <div class="contact-preview">
                                        <div class="last-message <%# GetUnreadCount(Eval("id")) > 0 ? "unread" : "" %>">
                                            <%# GetLastMessagePreview(Eval("id")) %>
                                        </div>
                                        <div class="message-indicators">
                                            <!-- Badge de notification -->
                                            <asp:Panel ID="pnlNotificationBadge" runat="server"
                                                       CssClass="notification-count"
                                                       Visible='<%# GetUnreadCount(Eval("id")) > 0 %>'>
                                                <%# GetUnreadCount(Eval("id")) %>
                                            </asp:Panel>

                                            <!-- Indicateur de statut du message -->
                                            <div class="message-status-icon">
                                                <%# GetMessageStatusIcon(Eval("id")) %>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </asp:LinkButton>
                                <!-- 🔽 Champ caché pour dire si c’est un groupe -->
 
                        </ItemTemplate>
                        </asp:ListView>
                    </div> <!-- Fin contacts-list -->

                </div>


                <!-- Main Chat Area -->
                <div class="chat-panel">

                    <div class="chat-header">
                        <div class="chat-header-info">
                            <div class="chat-header-name">
                                <asp:Label ID="lblHeader" runat="server" Text="Sélectionnez un contact pour discuter"></asp:Label>
                            </div>
                            <div class="chat-header-status">
                                <span class="online-indicator"></span>
                                <span>En ligne maintenant</span>
                            </div>
                        </div>
                        <asp:Label ID="lblId" Visible="false" runat="server" Text="0"></asp:Label>
                    </div>

                    <div class="chat-body">
                      <asp:Repeater ID="rptMessages" runat="server">
    <ItemTemplate>
        <div class='message-container <%# Eval("Expediteur").ToString() == "VotreNomComplet" ? "sent" : "received" %>'>
            <div class="message-header">
                <img class="avatar" src='<%# ResolveUrl("~/file/membr/") + Eval("Photomembre") %>' alt="Photo" />
                <span class="message-sender"><%# Eval("Expediteur") %></span>
                <span class="message-time"><%# Eval("DateEnvoi", "{0:HH:mm}") %></span>
            </div>

            <div class="message-bubble">
                <div class="message-body">
                    <p><%# Server.HtmlDecode(LinCom.Classe.EmojiManager.ConvertirEmojis(Eval("Contenu").ToString())) %></p>

                    <%-- Si le message contient une pièce jointe --%>
                    <asp:Panel runat="server" Visible='<%# !string.IsNullOrEmpty(Eval("AttachmentUrl").ToString()) %>'>
                        <div class="message-attachment">
                            <span class="attachment-icon"><%# GetFileIcon(Eval("AttachmentUrl").ToString()) %></span>
                            <div class="attachment-details">
                                <div class="attachment-name"><%# Eval("name") %></div>
                                <div class="attachment-size"><%# GetFileSize(Eval("AttachmentUrl").ToString()) %></div>
                            </div>
                            <a href='<%# Eval("AttachmentUrl") %>' class="attachment-download" target="_blank">Télécharger</a>
                        </div>
                    </asp:Panel>
                </div>
            </div>

            <%-- Affichage du statut de lecture pour les messages envoyés --%>
            <div class="message-footer">
                <span class="message-status status-read">✓✓</span>
            </div>
        </div>
    </ItemTemplate>
</asp:Repeater>

                    </div>

                    <div class="chat-footer">
                        <!-- Zone de saisie LinkedIn-style améliorée -->
                        <div class="linkedin-message-composer">
                            <!-- Barre d'outils supérieure -->
                            <div class="composer-toolbar">
                                <div class="toolbar-left">
                                    <button type="button" id="btnBold" class="format-btn" title="Gras (Ctrl+B)">
                                        <svg width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
                                            <path d="M15.6 10.79c.97-.67 1.65-1.77 1.65-2.79 0-2.26-1.75-4-4-4H7v14h7.04c2.09 0 3.71-1.7 3.71-3.79 0-1.52-.86-2.82-2.15-3.42zM10 6.5h3c.83 0 1.5.67 1.5 1.5s-.67 1.5-1.5 1.5h-3v-3zm3.5 9H10v-3h3.5c.83 0 1.5.67 1.5 1.5s-.67 1.5-1.5 1.5z"/>
                                        </svg>
                                    </button>
                                    <button type="button" id="btnItalic" class="format-btn" title="Italique (Ctrl+I)">
                                        <svg width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
                                            <path d="M10 4v3h2.21l-3.42 8H6v3h8v-3h-2.21l3.42-8H18V4z"/>
                                        </svg>
                                    </button>
                                    <button type="button" id="btnUnderline" class="format-btn" title="Souligné (Ctrl+U)">
                                        <svg width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
                                            <path d="M12 17c3.31 0 6-2.69 6-6V3h-2.5v8c0 1.93-1.57 3.5-3.5 3.5S8.5 12.93 8.5 11V3H6v8c0 3.31 2.69 6 6 6zm-7 2v2h14v-2H5z"/>
                                        </svg>
                                    </button>
                                    <div class="toolbar-separator"></div>
                                    <button type="button" id="btnEmoji" class="format-btn" title="Émojis">
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                                        </svg>
                                    </button>
                                    <button type="button" id="btnAttachment" class="format-btn" title="Joindre un fichier">
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                            <path d="M16.5 6v11.5c0 2.21-1.79 4-4 4s-4-1.79-4-4V5c0-1.38 1.12-2.5 2.5-2.5s2.5 1.12 2.5 2.5v10.5c0 .55-.45 1-1 1s-1-.45-1-1V6H10v9.5c0 1.38 1.12 2.5 2.5 2.5s2.5-1.12 2.5-2.5V5c0-2.21-1.79-4-4-4S7 2.79 7 5v12.5c0 3.04 2.46 5.5 5.5 5.5s5.5-2.46 5.5-5.5V6h-1.5z"/>
                                        </svg>
                                    </button>
                                    <input type="file" id="fileAttachment" style="display:none;" accept=".jpg,.jpeg,.png,.gif,.pdf,.doc,.docx,.txt,.zip,.rar,.mp3,.mp4,.avi" />
                                </div>
                                <div class="toolbar-right">
                                    <button type="button" id="btnGif" class="format-btn" title="GIF">
                                        <span style="font-size: 11px; font-weight: bold;">GIF</span>
                                    </button>
                                    <button type="button" id="btnMore" class="format-btn" title="Plus d'options">
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                            <path d="M12 8c1.1 0 2-.9 2-2s-.9-2-2-2-2 .9-2 2 .9 2 2 2zm0 2c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm0 6c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z"/>
                                        </svg>
                                    </button>
                                </div>
                            </div>

                            <!-- Zone de saisie principale -->
                            <div class="message-input-wrapper">
                                <div class="input-container">
                                    <div class="message-input-area">
                                        <textarea rows="1" runat="server" id="txtMessage"
                                                  placeholder="Écrivez un message..."
                                                  maxlength="1000"
                                                  class="linkedin-message-input"></textarea>
                                        <div class="input-placeholder" id="inputPlaceholder">Écrivez un message...</div>
                                    </div>
                                    <div class="send-button-container">
                                        <button type="button" runat="server" id="btnenvoie" onserverclick="btnenvoie_ServerClick" class="linkedin-send-btn" disabled>
                                            <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                                <path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"/>
                                            </svg>
                                        </button>
                                    </div>
                                </div>

                                <!-- Suggestions et mentions -->
                                <div class="suggestions-container" id="suggestionsContainer" style="display: none;">
                                    <div class="suggestions-list" id="suggestionsList">
                                        <!-- Les suggestions seront ajoutées dynamiquement -->
                                    </div>
                                </div>
                            </div>

                            <!-- Compteur de caractères et statut -->
                            <div class="composer-footer">
                                <div class="footer-left">
                                    <span class="char-counter" id="charCounter">0/1000</span>
                                    <span class="typing-indicator" id="typingIndicator" style="display: none;">
                                        <span class="typing-dots">
                                            <span></span><span></span><span></span>
                                        </span>
                                        En train d'écrire...
                                    </span>
                                </div>
                                <div class="footer-right">
                                    <button type="button" id="btnSchedule" class="schedule-btn" title="Programmer l'envoi">
                                        <svg width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
                                            <path d="M11.99 2C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2zM12 20c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8z"/>
                                            <path d="M12.5 7H11v6l5.25 3.15.75-1.23-4.5-2.67z"/>
                                        </svg>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Sélecteur d'émojis -->
                        <div id="emojiPicker" class="emoji-picker" style="display:none;">
                            <div class="emoji-header">
                                <span class="emoji-tab active" data-category="populaires">😊</span>
                                <span class="emoji-tab" data-category="visages">😀</span>
                                <span class="emoji-tab" data-category="gestes">👍</span>
                                <span class="emoji-tab" data-category="objets">❤️</span>
                                <span class="emoji-tab" data-category="nature">🌳</span>
                            </div>
                            <div class="emoji-content" id="emojiContent">
                                <!-- Émojis populaires par défaut -->
                                <div class="emoji-grid">
                                    <span class="emoji-item" data-emoji="😊">😊</span>
                                    <span class="emoji-item" data-emoji="😂">😂</span>
                                    <span class="emoji-item" data-emoji="❤️">❤️</span>
                                    <span class="emoji-item" data-emoji="👍">👍</span>
                                    <span class="emoji-item" data-emoji="😢">😢</span>
                                    <span class="emoji-item" data-emoji="😉">😉</span>
                                    <span class="emoji-item" data-emoji="🔥">🔥</span>
                                    <span class="emoji-item" data-emoji="🎉">🎉</span>
                                    <span class="emoji-item" data-emoji="😍">😍</span>
                                    <span class="emoji-item" data-emoji="👏">👏</span>
                                </div>
                            </div>
                        </div>

                        <!-- Prévisualisation de la pièce jointe -->
                        <div id="attachmentPreview" class="attachment-preview" style="display:none;">
                            <div class="attachment-info">
                                <span id="attachmentName"></span>
                                <span id="attachmentSize"></span>
                                <button type="button" id="btnRemoveAttachment" class="remove-btn">×</button>
                            </div>
                        </div>
                    </div>
                    <div class="message-counter">
                        <small id="charCount">0/1000 caractères</small>
                    </div>

                    <!-- Champ caché pour la pièce jointe -->
                    <asp:HiddenField ID="hdnAttachmentPath" runat="server" />
                </div>
            </div>
        </div>

    </main>

    <style>
        /* Variables CSS pour cohérence */
        :root {
            --linkedin-blue: #0a66c2;
            --linkedin-light-blue: #e7f3ff;
            --linkedin-dark-blue: #004182;
            --linkedin-gray: #666666;
            --linkedin-light-gray: #f3f2ef;
            --linkedin-border: #e0e0e0;
            --linkedin-white: #ffffff;
            --linkedin-green: #057642;
            --linkedin-hover: #f5f5f5;
            --message-sent: #0a66c2;
            --message-received: #f3f2ef;
            --shadow-light: 0 2px 4px rgba(0,0,0,0.08);
            --shadow-medium: 0 4px 12px rgba(0,0,0,0.15);
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            background-color: var(--linkedin-light-gray);
            margin: 0;
            padding: 0;
        }

        .chat-wrapper {
            display: flex;
            height: 85vh;
            max-height: 800px;
            border-radius: 8px;
            box-shadow: var(--shadow-medium);
            overflow: hidden;
            background: var(--linkedin-white);
            border: 1px solid var(--linkedin-border);
        }

        /* Panel de contacts - Style LinkedIn */
        .contacts-panel {
            width: 320px;
            background: var(--linkedin-white);
            border-right: 1px solid var(--linkedin-border);
            overflow-y: auto;
            display: flex;
            flex-direction: column;
        }

        .contacts-header {
            background: var(--linkedin-white);
            color: var(--linkedin-gray);
            padding: 16px 20px;
            font-weight: 600;
            font-size: 16px;
            border-bottom: 1px solid var(--linkedin-border);
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .contacts-search {
            padding: 16px 20px;
            border-bottom: 1px solid var(--linkedin-border);
            background: var(--linkedin-white);
        }

        .contacts-search input {
            width: 100%;
            padding: 8px 12px;
            border-radius: 4px;
            border: 1px solid var(--linkedin-border);
            font-size: 14px;
            background-color: var(--linkedin-light-gray);
            transition: all 0.2s ease;
        }

        .contacts-search input:focus {
            outline: none;
            border-color: var(--linkedin-blue);
            background-color: var(--linkedin-white);
            box-shadow: 0 0 0 2px var(--linkedin-light-blue);
        }

        .contact-item {
            padding: 12px 20px;
            display: flex;
            align-items: center;
            gap: 12px;
            cursor: pointer;
            transition: background-color 0.2s ease;
            border: none;
            background: none;
            text-decoration: none;
            color: inherit;
            position: relative;
        }

        .contact-item:hover {
            background-color: var(--linkedin-hover);
        }

        .contact-item.active {
            background-color: var(--linkedin-light-blue);
            border-right: 2px solid var(--linkedin-blue);
        }

        .contact-item img {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            object-fit: cover;
            border: 2px solid var(--linkedin-border);
        }

        .contact-info {
            flex: 1;
            min-width: 0;
        }

        .contact-name {
            font-weight: 600;
            font-size: 14px;
            color: #000;
            margin-bottom: 2px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .contact-status {
            font-size: 12px;
            color: var(--linkedin-gray);
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .online-indicator {
            width: 8px;
            height: 8px;
            background-color: var(--linkedin-green);
            border-radius: 50%;
            border: 1px solid var(--linkedin-white);
        }

        /* Panel de chat principal */
        .chat-panel {
            flex: 1;
            display: flex;
            flex-direction: column;
            background: var(--linkedin-white);
            min-width: 0;
        }

        .chat-header {
            background: var(--linkedin-white);
            padding: 16px 24px;
            border-bottom: 1px solid var(--linkedin-border);
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .chat-header-info {
            flex: 1;
        }

        .chat-header-name {
            font-weight: 600;
            font-size: 16px;
            color: #000;
            margin-bottom: 2px;
        }

        .chat-header-status {
            font-size: 12px;
            color: var(--linkedin-gray);
        }

        .chat-body {
            flex: 1;
            padding: 16px 24px;
            overflow-y: auto;
            display: flex;
            flex-direction: column;
            gap: 12px;
            background: var(--linkedin-white);
        }
        /* Messages - Style LinkedIn */
        .message-container {
            margin-bottom: 16px;
            max-width: 70%;
            display: flex;
            flex-direction: column;
            position: relative;
        }

        .message-container.sent {
            align-self: flex-end;
            align-items: flex-end;
        }

        .message-container.received {
            align-self: flex-start;
            align-items: flex-start;
        }

        .message-header {
            display: flex;
            align-items: center;
            margin-bottom: 4px;
            gap: 8px;
        }

        .message-container.sent .message-header {
            flex-direction: row-reverse;
        }

        .message-header .avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            object-fit: cover;
            border: 1px solid var(--linkedin-border);
        }

        .message-sender {
            font-size: 12px;
            font-weight: 600;
            color: var(--linkedin-gray);
        }

        .message-time {
            font-size: 11px;
            color: var(--linkedin-gray);
            margin-left: 8px;
        }

        .message-bubble {
            padding: 12px 16px;
            border-radius: 18px;
            font-size: 14px;
            line-height: 1.4;
            position: relative;
            word-wrap: break-word;
            max-width: 100%;
        }

        .message-container.sent .message-bubble {
            background: var(--message-sent);
            color: var(--linkedin-white);
            border-bottom-right-radius: 4px;
        }

        .message-container.received .message-bubble {
            background: var(--message-received);
            color: #000;
            border: 1px solid var(--linkedin-border);
            border-bottom-left-radius: 4px;
        }

        .message-body p {
            margin: 0;
            font-size: 14px;
            line-height: 1.4;
        }

        .message-footer {
            display: flex;
            align-items: center;
            justify-content: flex-end;
            margin-top: 4px;
            gap: 4px;
        }

        .message-container.received .message-footer {
            justify-content: flex-start;
        }

        /* Zone de saisie - Style LinkedIn */
        .chat-footer {
            padding: 16px 24px;
            background: var(--linkedin-white);
            border-top: 1px solid var(--linkedin-border);
        }

        .message-input-container {
            display: flex;
            align-items: flex-end;
            gap: 12px;
            position: relative;
            background: var(--linkedin-white);
            border: 1px solid var(--linkedin-border);
            border-radius: 24px;
            padding: 8px 16px;
            transition: all 0.2s ease;
        }

        .message-input-container:focus-within {
            border-color: var(--linkedin-blue);
            box-shadow: 0 0 0 2px var(--linkedin-light-blue);
        }

        .input-toolbar {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .toolbar-btn {
            background: transparent;
            border: none;
            border-radius: 50%;
            width: 32px;
            height: 32px;
            cursor: pointer;
            font-size: 16px;
            color: var(--linkedin-gray);
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .toolbar-btn:hover {
            background: var(--linkedin-hover);
            color: var(--linkedin-blue);
        }

        #txtMessage {
            flex: 1;
            border: none;
            outline: none;
            resize: none;
            font-size: 14px;
            line-height: 1.4;
            padding: 8px 0;
            background: transparent;
            font-family: inherit;
            min-height: 20px;
            max-height: 120px;
        }

        .send-btn {
            background: var(--linkedin-blue);
            color: var(--linkedin-white);
            border: none;
            border-radius: 50%;
            width: 36px;
            height: 36px;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
        }

        .send-btn:hover {
            background: var(--linkedin-dark-blue);
            transform: scale(1.05);
        }

        .send-btn:disabled {
            background: var(--linkedin-border);
            cursor: not-allowed;
            transform: none;
        }

        .message-counter {
            padding: 8px 16px;
            background: var(--linkedin-white);
            text-align: right;
            font-size: 12px;
            color: var(--linkedin-gray);
        }

        .typing-indicator {
            font-style: italic;
            color: var(--linkedin-gray);
            padding: 8px 24px;
            font-size: 12px;
            display: none;
        }

        /* Sélecteur d'émojis - Style LinkedIn */
        .emoji-picker {
            position: absolute;
            bottom: 100%;
            left: 0;
            width: 320px;
            height: 280px;
            background: var(--linkedin-white);
            border: 1px solid var(--linkedin-border);
            border-radius: 8px;
            box-shadow: var(--shadow-medium);
            z-index: 1000;
            overflow: hidden;
        }

        .emoji-header {
            display: flex;
            border-bottom: 1px solid var(--linkedin-border);
            padding: 12px 16px;
            background: var(--linkedin-light-gray);
        }

        .emoji-tab {
            padding: 8px 12px;
            cursor: pointer;
            border-radius: 6px;
            margin-right: 4px;
            transition: all 0.2s ease;
            font-size: 16px;
            border: 1px solid transparent;
        }

        .emoji-tab:hover {
            background: var(--linkedin-hover);
        }

        .emoji-tab.active {
            background: var(--linkedin-light-blue);
            border-color: var(--linkedin-blue);
        }

        .emoji-content {
            padding: 12px;
            height: 200px;
            overflow-y: auto;
        }

        .emoji-grid {
            display: grid;
            grid-template-columns: repeat(8, 1fr);
            gap: 4px;
        }

        .emoji-item {
            padding: 8px;
            text-align: center;
            cursor: pointer;
            border-radius: 6px;
            font-size: 18px;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .emoji-item:hover {
            background: var(--linkedin-hover);
            transform: scale(1.1);
        }

        /* Prévisualisation des pièces jointes - Style LinkedIn */
        .attachment-preview {
            background: var(--linkedin-light-gray);
            border: 1px solid var(--linkedin-border);
            border-radius: 8px;
            padding: 12px;
            margin-top: 8px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            gap: 12px;
        }

        .attachment-info {
            display: flex;
            align-items: center;
            gap: 12px;
            flex: 1;
            min-width: 0;
        }

        .attachment-info span {
            font-size: 13px;
            color: var(--linkedin-gray);
        }

        .remove-btn {
            background: #e74c3c;
            color: var(--linkedin-white);
            border: none;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            cursor: pointer;
            font-size: 14px;
            line-height: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;
        }

        .remove-btn:hover {
            background: #c0392b;
            transform: scale(1.1);
        }

        /* Statuts de messages - Style LinkedIn */
        .message-status {
            font-size: 11px;
            color: var(--linkedin-gray);
            display: flex;
            align-items: center;
            gap: 2px;
        }

        .status-sent { color: var(--linkedin-gray); }
        .status-delivered { color: var(--linkedin-blue); }
        .status-read { color: var(--linkedin-green); }

        /* Notifications - Style LinkedIn */
        .notification-badge {
            background: #e74c3c;
            color: var(--linkedin-white);
            border-radius: 50%;
            padding: 2px 6px;
            font-size: 10px;
            font-weight: 600;
            position: absolute;
            top: -2px;
            right: -2px;
            min-width: 16px;
            height: 16px;
            text-align: center;
            line-height: 12px;
            border: 2px solid var(--linkedin-white);
        }

        /* Pièces jointes dans les messages - Style LinkedIn */
        .message-attachment {
            background: var(--linkedin-light-gray);
            border: 1px solid var(--linkedin-border);
            border-radius: 8px;
            padding: 12px;
            margin: 8px 0;
            display: flex;
            align-items: center;
            gap: 12px;
            transition: all 0.2s ease;
        }

        .message-attachment:hover {
            background: var(--linkedin-hover);
        }

        .attachment-icon {
            font-size: 24px;
            color: var(--linkedin-blue);
        }

        .attachment-details {
            flex: 1;
            min-width: 0;
        }

        .attachment-name {
            font-weight: 600;
            color: #000;
            font-size: 13px;
            margin-bottom: 2px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .attachment-size {
            font-size: 11px;
            color: var(--linkedin-gray);
        }

        .attachment-download {
            background: var(--linkedin-blue);
            color: var(--linkedin-white);
            border: none;
            border-radius: 4px;
            padding: 6px 12px;
            cursor: pointer;
            text-decoration: none;
            font-size: 12px;
            font-weight: 600;
            transition: all 0.2s ease;
        }

        .attachment-download:hover {
            background: var(--linkedin-dark-blue);
            color: var(--linkedin-white);
            text-decoration: none;
            transform: translateY(-1px);
        }

        /* Images dans les messages - Style LinkedIn */
        .message-image {
            max-width: 240px;
            max-height: 240px;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.2s ease;
            border: 1px solid var(--linkedin-border);
        }

        .message-image:hover {
            transform: scale(1.02);
            box-shadow: var(--shadow-light);
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .chat-wrapper {
                height: 100vh;
                border-radius: 0;
            }

            .contacts-panel {
                width: 280px;
            }

            .emoji-picker {
                width: 280px;
                height: 240px;
            }

            .emoji-grid {
                grid-template-columns: repeat(6, 1fr);
            }

            .message-image {
                max-width: 180px;
                max-height: 180px;
            }

            .message-container {
                max-width: 85%;
            }

            .chat-body {
                padding: 12px 16px;
            }

            .chat-footer {
                padding: 12px 16px;
            }
        }

        @media (max-width: 480px) {
            .contacts-panel {
                width: 100%;
                position: absolute;
                z-index: 100;
                height: 100%;
            }

            .chat-panel {
                display: none;
            }

            .contacts-panel.hidden {
                display: none;
            }

            .chat-panel.active {
                display: flex;
            }
        }

        /* Scrollbar personnalisée */
        .contacts-panel::-webkit-scrollbar,
        .chat-body::-webkit-scrollbar,
        .emoji-content::-webkit-scrollbar {
            width: 6px;
        }

        .contacts-panel::-webkit-scrollbar-track,
        .chat-body::-webkit-scrollbar-track,
        .emoji-content::-webkit-scrollbar-track {
            background: var(--linkedin-light-gray);
        }

        .contacts-panel::-webkit-scrollbar-thumb,
        .chat-body::-webkit-scrollbar-thumb,
        .emoji-content::-webkit-scrollbar-thumb {
            background: var(--linkedin-border);
            border-radius: 3px;
        }

        .contacts-panel::-webkit-scrollbar-thumb:hover,
        .chat-body::-webkit-scrollbar-thumb:hover,
        .emoji-content::-webkit-scrollbar-thumb:hover {
            background: var(--linkedin-gray);
        }

        /* Animation pour les messages */
        .message-container {
            animation: fadeInUp 0.3s ease-out;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Effet de focus sur la zone de saisie */
        .message-input-container:focus-within .toolbar-btn {
            color: var(--linkedin-blue);
        }

        /* Amélioration de l'accessibilité */
        .contact-item:focus,
        .toolbar-btn:focus,
        .send-btn:focus {
            outline: 2px solid var(--linkedin-blue);
            outline-offset: 2px;
        }

        /* Style pour les liens dans les messages */
        .message-body a {
            color: var(--linkedin-blue);
            text-decoration: none;
        }

        .message-body a:hover {
            text-decoration: underline;
        }

        /* Indicateur de frappe */
        .typing-indicator.active {
            display: block;
            animation: pulse 1.5s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 0.5; }
            50% { opacity: 1; }
        }
    </style>

    <script type="text/javascript">
        // Script simplifié - La logique principale est dans messagerie-linkedin.js

        // Fonction appelée après les postbacks pour maintenir l'état
        function pageLoadComplete() {
            if (window.messagerieLinkedIn) {
                window.messagerieLinkedIn.scrollToBottom();
                window.messagerieLinkedIn.updateCharCount();
            }
        }

        // Appel après chaque postback
        Sys.WebForms.PageRequestManager.getInstance().add_endRequest(function() {
            pageLoadComplete();
        });








    </script>

</asp:Content>
