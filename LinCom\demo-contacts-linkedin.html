<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Démo - Contacts LinkedIn Style</title>
    <link href="css/messagerie-linkedin-style.css" rel="stylesheet" type="text/css" />
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #f3f2ef;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        .demo-container {
            max-width: 400px;
            margin: 0 auto;
            height: 600px;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .demo-header {
            text-align: center;
            margin-bottom: 20px;
            color: #333;
        }
    </style>
</head>
<body>
    <div class="demo-header">
        <h2>📱 Contacts LinkedIn Style - Démo</h2>
        <p>Aperçu des derniers messages comme sur LinkedIn</p>
    </div>

    <div class="demo-container">
        <!-- Panel de contacts avec header sticky -->
        <div class="contacts-panel" style="height: 100%;">
            <!-- En-tête Messages LinkedIn style -->
            <div class="linkedin-messages-header">
                <div class="messages-title">
                    <div class="title-main">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor" class="messages-icon">
                            <path d="M20 2H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h4l4 4 4-4h4c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-2 12H6v-2h12v2zm0-3H6V9h12v2zm0-3H6V6h12v2z"/>
                        </svg>
                        <span class="title-text">Messages</span>
                    </div>
                    <div class="messages-actions">
                        <button type="button" class="header-action-btn" title="Nouvelle conversation">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"/>
                            </svg>
                        </button>
                        <button type="button" class="header-action-btn" title="Plus d'options">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M12 8c1.1 0 2-.9 2-2s-.9-2-2-2-2 .9-2 2 .9 2 2 2zm0 2c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm0 6c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z"/>
                            </svg>
                        </button>
                    </div>
                </div>
                
                <!-- Onglets Messages/Conversations -->
                <div class="messages-tabs">
                    <button type="button" class="tab-btn active" data-tab="messages">
                        <span>Messages</span>
                        <span class="tab-count">12</span>
                    </button>
                    <button type="button" class="tab-btn" data-tab="conversations">
                        <span>Conversations</span>
                    </button>
                </div>
            </div>

            <!-- Barre de recherche LinkedIn style -->
            <div class="linkedin-search-container">
                <div class="search-input-wrapper">
                    <div class="search-icon">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
                        </svg>
                    </div>
                    <input type="text" placeholder="Rechercher dans les messages..." class="linkedin-search-input" />
                </div>
            </div>

            <!-- Liste des contacts scrollable -->
            <div class="contacts-list">
                <!-- Contact 1 - Avec messages non lus -->
                <div class="linkedin-contact-item active">
                    <div class="contact-avatar-container">
                        <img src="https://via.placeholder.com/48x48/0a66c2/ffffff?text=BS" alt="Photo de profil" class="contact-avatar" />
                    </div>
                    <div class="contact-main-info">
                        <div class="contact-header">
                            <div class="contact-name">Build Scalable Software with SOL...</div>
                            <div class="message-time">16:59</div>
                        </div>
                        <div class="contact-preview">
                            <div class="last-message unread">
                                <span class="message-prefix">~</span>
                                <span class="message-content">ScholarHat Team: 👋 We Are LIVE...</span>
                            </div>
                            <div class="message-indicators">
                                <div class="notification-count">2</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Contact 2 -->
                <div class="linkedin-contact-item">
                    <div class="contact-avatar-container">
                        <img src="https://via.placeholder.com/48x48/28a745/ffffff?text=CL" alt="Photo de profil" class="contact-avatar" />
                    </div>
                    <div class="contact-main-info">
                        <div class="contact-header">
                            <div class="contact-name">Community Laboratory</div>
                            <div class="message-time">16:54</div>
                        </div>
                        <div class="contact-preview">
                            <div class="last-message unread">
                                <span class="message-prefix">~</span>
                                <span class="message-content">Vital Taka: Tonight 08PM (Bujumbura...</span>
                            </div>
                            <div class="message-indicators">
                                <div class="notification-count">3</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Contact 3 -->
                <div class="linkedin-contact-item">
                    <div class="contact-avatar-container">
                        <img src="https://via.placeholder.com/48x48/6c757d/ffffff?text=IF" alt="Photo de profil" class="contact-avatar" />
                    </div>
                    <div class="contact-main-info">
                        <div class="contact-header">
                            <div class="contact-name">ir ferdinand</div>
                            <div class="message-time">16:49</div>
                        </div>
                        <div class="contact-preview">
                            <div class="last-message">
                                <span class="message-prefix">⏰</span>
                                <span class="message-content">📎 LinCom.m4a</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Contact 4 -->
                <div class="linkedin-contact-item">
                    <div class="contact-avatar-container">
                        <img src="https://via.placeholder.com/48x48/17a2b8/ffffff?text=GL" alt="Photo de profil" class="contact-avatar" />
                    </div>
                    <div class="contact-main-info">
                        <div class="contact-header">
                            <div class="contact-name">GL Promotion 2022-2023</div>
                            <div class="message-time">16:29</div>
                        </div>
                        <div class="contact-preview">
                            <div class="last-message">
                                <span class="message-prefix">~</span>
                                <span class="message-content">Scorpion🦂: Si vous connaissez un ba...</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Contact 5 -->
                <div class="linkedin-contact-item">
                    <div class="contact-avatar-container">
                        <img src="https://via.placeholder.com/48x48/ffc107/ffffff?text=SG" alt="Photo de profil" class="contact-avatar" />
                    </div>
                    <div class="contact-main-info">
                        <div class="contact-header">
                            <div class="contact-name">SkillGate_CCNA_Group4</div>
                            <div class="message-time">16:14</div>
                        </div>
                        <div class="contact-preview">
                            <div class="last-message">
                                <span class="message-prefix"></span>
                                <span class="message-content">Skillgate Group: 🎯 Rappel – 3e séanc...</span>
                            </div>
                            <div class="message-indicators">
                                <div class="notification-count">6</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Contact 6 -->
                <div class="linkedin-contact-item">
                    <div class="contact-avatar-container">
                        <img src="https://via.placeholder.com/48x48/dc3545/ffffff?text=BC" alt="Photo de profil" class="contact-avatar" />
                    </div>
                    <div class="contact-main-info">
                        <div class="contact-header">
                            <div class="contact-name">63535 BIBLIOTHÈQUE CHRÉTIEN...</div>
                            <div class="message-time">15:32</div>
                        </div>
                        <div class="contact-preview">
                            <div class="last-message">
                                <span class="message-prefix"></span>
                                <span class="message-content">Justin: 📚📚📚 Voila les livres de co...</span>
                            </div>
                            <div class="message-indicators">
                                <div class="notification-count">25</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Contact 7 -->
                <div class="linkedin-contact-item">
                    <div class="contact-avatar-container">
                        <img src="https://via.placeholder.com/48x48/6f42c1/ffffff?text=FL" alt="Photo de profil" class="contact-avatar" />
                    </div>
                    <div class="contact-main-info">
                        <div class="contact-header">
                            <div class="contact-name">Famille Lubangu & Rehema</div>
                            <div class="message-time">12:47</div>
                        </div>
                        <div class="contact-preview">
                            <div class="last-message">
                                <span class="message-prefix"></span>
                                <span class="message-content">odettemina16@gmail: 😊 Le procès...</span>
                            </div>
                            <div class="message-indicators">
                                <div class="notification-count">1</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Contact 8 -->
                <div class="linkedin-contact-item">
                    <div class="contact-avatar-container">
                        <img src="https://via.placeholder.com/48x48/fd7e14/ffffff?text=FG" alt="Photo de profil" class="contact-avatar" />
                    </div>
                    <div class="contact-main-info">
                        <div class="contact-header">
                            <div class="contact-name">Formation on Graphic Design</div>
                            <div class="message-time">09:12</div>
                        </div>
                        <div class="contact-preview">
                            <div class="last-message">
                                <span class="message-prefix"></span>
                                <span class="message-content">Vous: Merci pour les informations</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div style="margin-top: 20px; text-align: center; color: #666; font-size: 14px;">
        <p>💡 <strong>Design LinkedIn :</strong> Aperçu des derniers messages avec heure et badges de notification</p>
    </div>

    <script>
        // Gestion des interactions
        document.addEventListener('DOMContentLoaded', function() {
            // Gestion des onglets
            document.querySelectorAll('.tab-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    document.querySelectorAll('.tab-btn').forEach(b => b.classList.remove('active'));
                    this.classList.add('active');
                });
            });

            // Gestion des contacts
            document.querySelectorAll('.linkedin-contact-item').forEach(contact => {
                contact.addEventListener('click', function() {
                    document.querySelectorAll('.linkedin-contact-item').forEach(c => c.classList.remove('active'));
                    this.classList.add('active');
                });
            });
        });
    </script>
</body>
</html>
